2025-07-15 01:47:43,014 - INFO - تم بدء تشغيل التطبيق
2025-07-15 01:47:43,014 - INFO - تم بدء تشغيل التطبيق
2025-07-15 01:47:43,195 - INFO - تم إنشاء المجلدات الأساسية
2025-07-15 01:47:43,310 - INFO - تم إنشاء المجلدات الأساسية
2025-07-15 01:47:43,846 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-15 01:47:43,846 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-15 01:47:43,847 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-15 01:47:43,875 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-15 01:47:45,123 - ERROR - خطأ في إنشاء النسخة الاحتياطية: [WinError 32] The process cannot access the file because it is being used by another process
2025-07-15 01:47:45,249 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250715_014744.db
2025-07-15 01:47:47,098 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-15 01:47:47,106 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-15 01:47:58,392 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-15 01:47:58,392 - INFO - تم إعداد التطبيق بنجاح
2025-07-15 01:47:58,392 - INFO - تم إعداد التطبيق بنجاح
2025-07-15 01:47:58,393 - INFO - بدء إغلاق التطبيق
2025-07-15 01:47:58,393 - INFO - بدء إغلاق التطبيق
2025-07-15 01:47:58,393 - ERROR - خطأ أثناء إغلاق التطبيق: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-15 01:47:58,395 - ERROR - خطأ أثناء إغلاق التطبيق: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-15 01:48:32,067 - INFO - تم بدء تشغيل التطبيق
2025-07-15 01:48:32,132 - INFO - تم إنشاء المجلدات الأساسية
2025-07-15 01:48:32,140 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-15 01:48:32,140 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-15 01:48:32,244 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250715_014832.db
2025-07-15 01:48:32,251 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-15 01:48:32,883 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-15 01:48:32,888 - INFO - تم إعداد التطبيق بنجاح
2025-07-15 01:48:34,917 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-15 01:48:35,730 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-15 01:48:35,730 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-15 01:48:36,000 - INFO - معالجة الأمر: /start
2025-07-15 01:48:36,497 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 01:48:38,737 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 01:48:38,741 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 01:48:38,998 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 01:48:39,219 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 01:48:50,595 - INFO - تم اختيار النظام: hotspot
2025-07-15 01:48:51,252 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-15 01:48:51,444 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 01:48:51,445 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 01:48:51,655 - INFO - تم تسجيل العملية: اختيار النظام - تم اختيار نظام hotspot
2025-07-15 01:48:52,453 - INFO - تم تفعيل إشعارات Telegram للعمليات والأخطاء
2025-07-15 01:49:26,269 - INFO - بدء اختبار الاتصال مع MikroTik
2025-07-15 01:49:26,270 - INFO - محاولة الاتصال بـ *********:8729 (SSL)
2025-07-15 01:49:26,340 - ERROR - خطأ في الاتصال - النوع: SSLError, الرسالة: [SSL: SSLV3_ALERT_HANDSHAKE_FAILURE] sslv3 alert handshake failure (_ssl.c:1028)
2025-07-15 01:49:26,357 - INFO - تم قطع الاتصال مع MikroTik
2025-07-15 01:49:28,890 - INFO - تم تسجيل العملية: اختبار الاتصال - فشل الاتصال
2025-07-15 01:49:42,453 - INFO - 🔄 تغيير إعدادات SSL - قطع أي اتصالات موجودة...
2025-07-15 01:49:42,461 - INFO - ✅ تغيير إعدادات SSL - تم تنظيف حالة الاتصال بنجاح
2025-07-15 01:49:42,564 - INFO - تم إلغاء تفعيل SSL - المنفذ الافتراضي: 8728
2025-07-15 01:49:44,189 - INFO - بدء اختبار الاتصال مع MikroTik
2025-07-15 01:49:44,190 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-15 01:49:44,250 - INFO - نجح الاتصال مع *********
2025-07-15 01:49:47,117 - INFO - تم تسجيل العملية: اختبار الاتصال - نجح الاتصال مع MikroTik
2025-07-15 01:49:48,860 - INFO - استخدام الاتصال الحالي
2025-07-15 01:49:50,562 - INFO - تم جلب 4 بروفايل يدوياً من النظام: hotspot
2025-07-15 01:49:57,470 - INFO - بدء اختبار الاتصال مع MikroTik
2025-07-15 01:49:57,473 - INFO - استخدام الاتصال الحالي
2025-07-15 01:49:59,219 - INFO - تم تسجيل العملية: اختبار الاتصال - نجح الاتصال مع MikroTik
2025-07-15 01:50:13,607 - INFO - معالجة callback: select_system_hs
2025-07-15 01:50:14,101 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-15 01:50:15,891 - INFO - معالجة callback: independent_template_hs_10
2025-07-15 01:50:16,383 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-15 01:50:16,387 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 01:50:16,388 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 01:50:16,407 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 01:50:16,409 - INFO - ✅ تم تحديث Host: ********* → ***********
2025-07-15 01:50:16,410 - INFO - ✅ تم تحديث Username: 11 → saye
2025-07-15 01:50:16,411 - INFO - ✅ تم تحديث Password
2025-07-15 01:50:16,413 - INFO - ✅ تم تحديث Port: 8728 → 8729
2025-07-15 01:50:16,413 - INFO - ✅ تم تحديث SSL: مفعل
2025-07-15 01:50:16,414 - INFO - 🔄 تم جدولة تحديث معلومات الخادم في البوت
2025-07-15 01:50:16,414 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 5 إعدادات
2025-07-15 01:50:16,443 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 77 إعدادات
2025-07-15 01:50:16,443 - INFO - ✅ تم تطبيق 77 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 01:50:16,445 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 01:50:16,450 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 01:50:16,454 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 01:50:16,455 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 01:50:16,714 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-15 01:50:19,834 - INFO - معالجة callback: independent_create_hs_10_lightning
2025-07-15 01:50:22,275 - INFO - معالجة callback: independent_custom_hs_10_lightning
2025-07-15 01:50:45,849 - INFO - معالجة الأمر: 1
2025-07-15 01:50:46,119 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 01:50:46,122 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 01:50:46,143 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 01:50:46,144 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 01:50:46,169 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 01:50:46,169 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 01:50:46,171 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 01:50:46,175 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 01:50:46,406 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 01:50:46,406 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 01:50:46,430 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 01:50:46,440 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 01:50:46,462 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 01:50:46,463 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 01:50:46,466 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 01:50:46,471 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 01:50:46,472 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 1
2025-07-15 01:50:46,473 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-15 01:50:46,473 - INFO - ⚡ بدء البرق التلقائي الموحد لنظام Hotspot
2025-07-15 01:50:46,505 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 1
2025-07-15 01:50:46,506 - INFO - ⚡ البرق الموحد: العدد المطلوب 1 حساب لنظام Hotspot
2025-07-15 01:50:46,507 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 1
2025-07-15 01:50:46,507 - INFO - ⚡ البرق الموحد: تم توليد 1 حساب لنظام Hotspot
2025-07-15 01:50:46,738 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 1
2025-07-15 01:50:46,965 - INFO - تم إنشاء PDF بنجاح: exports\كروت_(10)_ب20-1 كارت-15-07-2025-01-50-46-hotspot.pdf
2025-07-15 01:50:46,967 - INFO - تم حفظ ملف PDF: exports\كروت_(10)_ب20-1 كارت-15-07-2025-01-50-46-hotspot.pdf
2025-07-15 01:50:49,515 - INFO - ⚡ البرق الموحد: تم حفظ PDF: exports\كروت_(10)_ب20-1 كارت-15-07-2025-01-50-46-hotspot.pdf
2025-07-15 01:50:49,572 - INFO - ⚡ البرق الموحد: تم حفظ ملف .rsc الاحتياطي: exports\كروت_(10)_ب20-1 كارت-15-07-2025-01-50-46-hotspot.rsc
2025-07-15 01:50:50,141 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-1 كارت-15-07-2025-01-50-46-hotspot.pdf
2025-07-15 01:50:50,142 - INFO - ⚡ البرق الموحد: تم إرسال ملف PDF عبر التلجرام: exports\كروت_(10)_ب20-1 كارت-15-07-2025-01-50-46-hotspot.pdf
2025-07-15 01:50:50,380 - INFO - ⚡ البرق الموحد: بدء إرسال الكروت إلى MikroTik باستخدام آلية send_to_mikrotik
2025-07-15 01:50:50,385 - INFO - استخدام الاتصال الحالي
2025-07-15 01:50:50,491 - ERROR - ❌ فشل في إرسال المستخدم 02681363517: ('Error "input does not match any value of profile" executing command b\'/ip/hotspot/user/add =name=02681363517 =password= =profile=CARD=ALLLLLLL1 =comment= =server=all =limit-bytes-total=6442450944 =email=<EMAIL> .tag=10\'', b'input does not match any value of profile')
2025-07-15 01:50:50,498 - ERROR - ❌ كرت واحد: خطأ عام في الإرسال: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-15 01:50:50,499 - WARNING - ⚡ البرق الموحد: تم إنشاء 1 كارت وحفظ PDF، لكن فشل إرسال الكروت إلى MikroTik
2025-07-15 01:50:51,473 - INFO - تم إرسال 1 كرت عبر البرق الموحد باستخدام قالب 10
2025-07-15 01:51:47,608 - ERROR - خطأ في معالجة تحديثات التلجرام: HTTPSConnectionPool(host='api.telegram.org', port=443): Max retries exceeded with url: /bot5161769536:AAHFeXIB-kCvIfo_NwfR7dwiWOUgXJF-p-Y/getUpdates?offset=858838660&timeout=10 (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x000001F870C86850>: Failed to resolve 'api.telegram.org' ([Errno 8] getaddrinfo failed)"))
2025-07-15 01:51:47,820 - ERROR - خطأ في إرسال رسالة Telegram: HTTP Error 400: Bad Request
2025-07-15 01:54:25,671 - INFO - استخدام الاتصال الحالي
2025-07-15 01:54:27,923 - INFO - تم جلب 4 بروفايل يدوياً من النظام: hotspot
2025-07-15 01:54:33,884 - INFO - تم حفظ القالب: 10
2025-07-15 01:54:34,393 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 01:54:34,446 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 01:54:34,700 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 01:54:34,700 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 01:54:36,550 - INFO - تم حفظ القالب: 10
2025-07-15 01:54:37,052 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 01:54:37,054 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 01:54:37,307 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 01:54:37,307 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 01:54:40,880 - INFO - معالجة callback: select_system_hs
2025-07-15 01:54:41,402 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-15 01:54:42,911 - INFO - معالجة callback: independent_template_hs_10
2025-07-15 01:54:43,424 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-15 01:54:43,425 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 01:54:43,425 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 01:54:43,443 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 01:54:43,452 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 01:54:43,475 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 01:54:43,478 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 01:54:43,481 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 01:54:43,484 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 01:54:43,488 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 01:54:43,489 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 01:54:43,775 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-15 01:54:46,785 - INFO - معالجة callback: independent_create_hs_10_lightning
2025-07-15 01:54:50,536 - INFO - معالجة callback: independent_count_hs_10_lightning_5
2025-07-15 01:54:51,070 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 01:54:51,070 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 01:54:51,091 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 01:54:51,092 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 01:54:51,114 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 01:54:51,117 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 01:54:51,120 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 01:54:51,124 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 01:54:51,369 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 01:54:51,369 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 01:54:51,387 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 01:54:51,389 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 01:54:51,410 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 01:54:51,412 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 01:54:51,417 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 01:54:51,421 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 01:54:51,422 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 1
2025-07-15 01:54:51,422 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-15 01:54:51,423 - INFO - ⚡ بدء البرق التلقائي الموحد لنظام Hotspot
2025-07-15 01:54:51,427 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 5
2025-07-15 01:54:51,427 - INFO - ⚡ البرق الموحد: العدد المطلوب 5 حساب لنظام Hotspot
2025-07-15 01:54:51,428 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 5
2025-07-15 01:54:51,428 - INFO - ⚡ البرق الموحد: تم توليد 5 حساب لنظام Hotspot
2025-07-15 01:54:51,541 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 5
2025-07-15 01:54:51,620 - INFO - تم إنشاء PDF بنجاح: exports\كروت_(10)_ب20-5 كارت-15-07-2025-01-54-51-hotspot.pdf
2025-07-15 01:54:51,620 - INFO - تم حفظ ملف PDF: exports\كروت_(10)_ب20-5 كارت-15-07-2025-01-54-51-hotspot.pdf
2025-07-15 01:54:51,670 - INFO - ⚡ البرق الموحد: تم حفظ PDF: exports\كروت_(10)_ب20-5 كارت-15-07-2025-01-54-51-hotspot.pdf
2025-07-15 01:54:51,681 - INFO - ⚡ البرق الموحد: تم حفظ ملف .rsc الاحتياطي: exports\كروت_(10)_ب20-5 كارت-15-07-2025-01-54-51-hotspot.rsc
2025-07-15 01:54:52,437 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-5 كارت-15-07-2025-01-54-51-hotspot.pdf
2025-07-15 01:54:52,438 - INFO - ⚡ البرق الموحد: تم إرسال ملف PDF عبر التلجرام: exports\كروت_(10)_ب20-5 كارت-15-07-2025-01-54-51-hotspot.pdf
2025-07-15 01:54:52,694 - INFO - ⚡ البرق الموحد: بدء إرسال الكروت إلى MikroTik باستخدام آلية send_to_mikrotik
2025-07-15 01:54:52,701 - INFO - استخدام الاتصال الحالي
2025-07-15 01:54:52,800 - INFO - ✅ تم إرسال المستخدم: 02268602460
2025-07-15 01:54:52,857 - INFO - ✅ تم إرسال المستخدم: 02355294606
2025-07-15 01:54:52,917 - INFO - ✅ تم إرسال المستخدم: 02667877264
2025-07-15 01:54:52,978 - INFO - ✅ تم إرسال المستخدم: 02132896597
2025-07-15 01:54:53,039 - INFO - ✅ تم إرسال المستخدم: 02074638172
2025-07-15 01:54:53,040 - ERROR - ❌ كرت واحد: خطأ عام في الإرسال: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-15 01:54:53,041 - WARNING - ⚡ البرق الموحد: تم إنشاء 5 كارت وحفظ PDF، لكن فشل إرسال الكروت إلى MikroTik
2025-07-15 01:54:56,423 - INFO - تم إرسال 5 كرت عبر البرق الموحد باستخدام قالب 10
2025-07-15 01:55:50,529 - INFO - معالجة الأمر: /start
2025-07-15 01:55:50,820 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 01:55:52,254 - INFO - معالجة callback: select_system_hs
2025-07-15 01:55:52,901 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-15 01:55:54,288 - INFO - معالجة callback: independent_template_hs_10
2025-07-15 01:55:54,905 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-15 01:55:54,906 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 01:55:54,906 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 01:55:54,927 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 01:55:54,929 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 01:55:54,955 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 01:55:54,956 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 01:55:54,959 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 01:55:54,965 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 01:55:54,969 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 01:55:54,971 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 01:55:55,242 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-15 01:55:57,291 - INFO - معالجة callback: independent_create_hs_10_lightning
2025-07-15 01:55:59,227 - INFO - معالجة callback: independent_count_hs_10_lightning_200
2025-07-15 01:55:59,700 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 01:55:59,701 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 01:55:59,721 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 01:55:59,722 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 01:55:59,746 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 01:55:59,748 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 01:55:59,750 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 01:55:59,754 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 01:56:00,035 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 01:56:00,035 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 01:56:00,057 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 01:56:00,061 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 01:56:00,085 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 01:56:00,085 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 01:56:00,088 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 01:56:00,095 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 01:56:00,097 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 1
2025-07-15 01:56:00,098 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-15 01:56:00,098 - INFO - ⚡ بدء البرق التلقائي الموحد لنظام Hotspot
2025-07-15 01:56:00,104 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-15 01:56:00,105 - INFO - ⚡ البرق الموحد: العدد المطلوب 200 حساب لنظام Hotspot
2025-07-15 01:56:00,106 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-15 01:56:00,110 - INFO - ⚡ البرق الموحد: تم توليد 200 حساب لنظام Hotspot
2025-07-15 01:56:00,243 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-15 01:56:00,277 - INFO - تم إنشاء PDF بنجاح: exports\كروت_(10)_ب20-200 كارت-15-07-2025-01-56-00-hotspot.pdf
2025-07-15 01:56:00,279 - INFO - تم حفظ ملف PDF: exports\كروت_(10)_ب20-200 كارت-15-07-2025-01-56-00-hotspot.pdf
2025-07-15 01:56:00,322 - INFO - ⚡ البرق الموحد: تم حفظ PDF: exports\كروت_(10)_ب20-200 كارت-15-07-2025-01-56-00-hotspot.pdf
2025-07-15 01:56:00,334 - INFO - ⚡ البرق الموحد: تم حفظ ملف .rsc الاحتياطي: exports\كروت_(10)_ب20-200 كارت-15-07-2025-01-56-00-hotspot.rsc
2025-07-15 01:56:01,039 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-15-07-2025-01-56-00-hotspot.pdf
2025-07-15 01:56:01,063 - INFO - ⚡ البرق الموحد: تم إرسال ملف PDF عبر التلجرام: exports\كروت_(10)_ب20-200 كارت-15-07-2025-01-56-00-hotspot.pdf
2025-07-15 01:56:01,337 - INFO - ⚡ البرق الموحد: بدء إرسال الكروت إلى MikroTik باستخدام آلية send_to_mikrotik
2025-07-15 01:56:01,411 - INFO - استخدام الاتصال الحالي
2025-07-15 01:56:01,575 - INFO - ✅ تم إرسال المستخدم: 02472068756
2025-07-15 01:56:01,667 - INFO - ✅ تم إرسال المستخدم: 02039859454
2025-07-15 01:56:01,768 - INFO - ✅ تم إرسال المستخدم: 02607214366
2025-07-15 01:56:01,852 - INFO - ✅ تم إرسال المستخدم: 02212865694
2025-07-15 01:56:01,906 - INFO - ✅ تم إرسال المستخدم: 02200856461
2025-07-15 01:56:01,955 - INFO - ✅ تم إرسال المستخدم: 02976343122
2025-07-15 01:56:02,007 - INFO - ✅ تم إرسال المستخدم: 02638911855
2025-07-15 01:56:02,111 - INFO - ✅ تم إرسال المستخدم: 02241684767
2025-07-15 01:56:02,208 - INFO - ✅ تم إرسال المستخدم: 02775701954
2025-07-15 01:56:02,296 - INFO - ✅ تم إرسال المستخدم: 02485492385
2025-07-15 01:56:02,400 - INFO - ✅ تم إرسال المستخدم: 02018446508
2025-07-15 01:56:02,488 - INFO - ✅ تم إرسال المستخدم: 02410634118
2025-07-15 01:56:02,526 - INFO - ✅ تم إرسال المستخدم: 02574548748
2025-07-15 01:56:02,612 - INFO - ✅ تم إرسال المستخدم: 02423172730
2025-07-15 01:56:02,670 - INFO - ✅ تم إرسال المستخدم: 02861384696
2025-07-15 01:56:02,720 - INFO - ✅ تم إرسال المستخدم: 02812504556
2025-07-15 01:56:02,798 - INFO - ✅ تم إرسال المستخدم: 02772513284
2025-07-15 01:56:02,864 - INFO - ✅ تم إرسال المستخدم: 02039298168
2025-07-15 01:56:02,935 - INFO - ✅ تم إرسال المستخدم: 02450369128
2025-07-15 01:56:02,978 - INFO - ✅ تم إرسال المستخدم: 02472713046
2025-07-15 01:56:03,024 - INFO - ✅ تم إرسال المستخدم: 02135132366
2025-07-15 01:56:03,068 - INFO - ✅ تم إرسال المستخدم: 02322460395
2025-07-15 01:56:03,112 - INFO - ✅ تم إرسال المستخدم: 02741349637
2025-07-15 01:56:03,155 - INFO - ✅ تم إرسال المستخدم: 02265474671
2025-07-15 01:56:03,196 - INFO - ✅ تم إرسال المستخدم: 02619931675
2025-07-15 01:56:03,238 - INFO - ✅ تم إرسال المستخدم: 02663059059
2025-07-15 01:56:03,286 - INFO - ✅ تم إرسال المستخدم: 02449793676
2025-07-15 01:56:03,383 - INFO - ✅ تم إرسال المستخدم: 02724119424
2025-07-15 01:56:03,502 - INFO - ✅ تم إرسال المستخدم: 02136398023
2025-07-15 01:56:03,571 - INFO - ✅ تم إرسال المستخدم: 02202450764
2025-07-15 01:56:03,632 - INFO - ✅ تم إرسال المستخدم: 02480503718
2025-07-15 01:56:03,739 - INFO - ✅ تم إرسال المستخدم: 02861423787
2025-07-15 01:56:03,882 - INFO - ✅ تم إرسال المستخدم: 02651858726
2025-07-15 01:56:03,934 - INFO - ✅ تم إرسال المستخدم: 02579048168
2025-07-15 01:56:03,982 - INFO - ✅ تم إرسال المستخدم: 02892630156
2025-07-15 01:56:04,033 - INFO - ✅ تم إرسال المستخدم: 02761500622
2025-07-15 01:56:04,088 - INFO - ✅ تم إرسال المستخدم: 02170085582
2025-07-15 01:56:04,166 - INFO - ✅ تم إرسال المستخدم: 02054632066
2025-07-15 01:56:04,233 - INFO - ✅ تم إرسال المستخدم: 02760135602
2025-07-15 01:56:04,285 - INFO - ✅ تم إرسال المستخدم: 02264093995
2025-07-15 01:56:04,350 - INFO - ✅ تم إرسال المستخدم: 02576831674
2025-07-15 01:56:04,406 - INFO - ✅ تم إرسال المستخدم: 02741739438
2025-07-15 01:56:04,455 - INFO - ✅ تم إرسال المستخدم: 02422718287
2025-07-15 01:56:04,497 - INFO - ✅ تم إرسال المستخدم: 02601107371
2025-07-15 01:56:04,558 - INFO - ✅ تم إرسال المستخدم: 02629868611
2025-07-15 01:56:04,624 - INFO - ✅ تم إرسال المستخدم: 02730102113
2025-07-15 01:56:04,752 - INFO - ✅ تم إرسال المستخدم: 02613803480
2025-07-15 01:56:04,830 - INFO - ✅ تم إرسال المستخدم: 02767153156
2025-07-15 01:56:04,925 - INFO - ✅ تم إرسال المستخدم: 02151278600
2025-07-15 01:56:05,008 - INFO - ✅ تم إرسال المستخدم: 02156705450
2025-07-15 01:56:05,077 - INFO - ✅ تم إرسال المستخدم: 02191905121
2025-07-15 01:56:05,170 - INFO - ✅ تم إرسال المستخدم: 02844766798
2025-07-15 01:56:05,259 - INFO - ✅ تم إرسال المستخدم: 02474143387
2025-07-15 01:56:05,327 - INFO - ✅ تم إرسال المستخدم: 02245524187
2025-07-15 01:56:05,413 - INFO - ✅ تم إرسال المستخدم: 02603229572
2025-07-15 01:56:05,536 - INFO - ✅ تم إرسال المستخدم: 02345886050
2025-07-15 01:56:05,642 - INFO - ✅ تم إرسال المستخدم: 02200690085
2025-07-15 01:56:05,736 - INFO - ✅ تم إرسال المستخدم: 02959982969
2025-07-15 01:56:05,794 - INFO - ✅ تم إرسال المستخدم: 02543351474
2025-07-15 01:56:05,866 - INFO - ✅ تم إرسال المستخدم: 02836482961
2025-07-15 01:56:05,962 - INFO - ✅ تم إرسال المستخدم: 02982000076
2025-07-15 01:56:06,050 - INFO - ✅ تم إرسال المستخدم: 02017685299
2025-07-15 01:56:06,176 - INFO - ✅ تم إرسال المستخدم: 02928902039
2025-07-15 01:56:06,322 - INFO - ✅ تم إرسال المستخدم: 02962553824
2025-07-15 01:56:06,485 - INFO - ✅ تم إرسال المستخدم: 02359990052
2025-07-15 01:56:06,596 - INFO - ✅ تم إرسال المستخدم: 02120265900
2025-07-15 01:56:06,687 - INFO - ✅ تم إرسال المستخدم: 02735141317
2025-07-15 01:56:06,778 - INFO - ✅ تم إرسال المستخدم: 02949800444
2025-07-15 01:56:06,861 - INFO - ✅ تم إرسال المستخدم: 02673785195
2025-07-15 01:56:06,914 - INFO - ✅ تم إرسال المستخدم: 02633651404
2025-07-15 01:56:06,966 - INFO - ✅ تم إرسال المستخدم: 02935107333
2025-07-15 01:56:07,011 - INFO - ✅ تم إرسال المستخدم: 02679300836
2025-07-15 01:56:07,062 - INFO - ✅ تم إرسال المستخدم: 02774719463
2025-07-15 01:56:07,119 - INFO - ✅ تم إرسال المستخدم: 02958720433
2025-07-15 01:56:07,165 - INFO - ✅ تم إرسال المستخدم: 02961927613
2025-07-15 01:56:07,228 - INFO - ✅ تم إرسال المستخدم: 02148749929
2025-07-15 01:56:07,265 - INFO - ✅ تم إرسال المستخدم: 02692276535
2025-07-15 01:56:07,307 - INFO - ✅ تم إرسال المستخدم: 02722644928
2025-07-15 01:56:07,357 - INFO - ✅ تم إرسال المستخدم: 02077061962
2025-07-15 01:56:07,414 - INFO - ✅ تم إرسال المستخدم: 02371496899
2025-07-15 01:56:07,475 - INFO - ✅ تم إرسال المستخدم: 02643574656
2025-07-15 01:56:07,550 - INFO - ✅ تم إرسال المستخدم: 02916306112
2025-07-15 01:56:07,608 - INFO - ✅ تم إرسال المستخدم: 02927904301
2025-07-15 01:56:07,652 - INFO - ✅ تم إرسال المستخدم: 02858853261
2025-07-15 01:56:07,697 - INFO - ✅ تم إرسال المستخدم: 02571184475
2025-07-15 01:56:07,739 - INFO - ✅ تم إرسال المستخدم: 02829833981
2025-07-15 01:56:07,785 - INFO - ✅ تم إرسال المستخدم: 02502858535
2025-07-15 01:56:07,838 - INFO - ✅ تم إرسال المستخدم: 02783355472
2025-07-15 01:56:07,875 - INFO - ✅ تم إرسال المستخدم: 02157380565
2025-07-15 01:56:07,928 - INFO - ✅ تم إرسال المستخدم: 02218350939
2025-07-15 01:56:07,965 - INFO - ✅ تم إرسال المستخدم: 02340353245
2025-07-15 01:56:08,007 - INFO - ✅ تم إرسال المستخدم: 02871960291
2025-07-15 01:56:08,065 - INFO - ✅ تم إرسال المستخدم: 02177497141
2025-07-15 01:56:08,118 - INFO - ✅ تم إرسال المستخدم: 02160354325
2025-07-15 01:56:08,166 - INFO - ✅ تم إرسال المستخدم: 02982889532
2025-07-15 01:56:08,205 - INFO - ✅ تم إرسال المستخدم: 02281560874
2025-07-15 01:56:08,251 - INFO - ✅ تم إرسال المستخدم: 02431828808
2025-07-15 01:56:08,297 - INFO - ✅ تم إرسال المستخدم: 02668343391
2025-07-15 01:56:08,335 - INFO - ✅ تم إرسال المستخدم: 02038004860
2025-07-15 01:56:08,375 - INFO - ✅ تم إرسال المستخدم: 02534670227
2025-07-15 01:56:08,417 - INFO - ✅ تم إرسال المستخدم: 02410731377
2025-07-15 01:56:08,463 - INFO - ✅ تم إرسال المستخدم: 02856501510
2025-07-15 01:56:08,515 - INFO - ✅ تم إرسال المستخدم: 02830774182
2025-07-15 01:56:08,589 - INFO - ✅ تم إرسال المستخدم: 02142391284
2025-07-15 01:56:08,650 - INFO - ✅ تم إرسال المستخدم: 02510113280
2025-07-15 01:56:08,721 - INFO - ✅ تم إرسال المستخدم: 02264231798
2025-07-15 01:56:08,775 - INFO - ✅ تم إرسال المستخدم: 02135208634
2025-07-15 01:56:08,815 - INFO - ✅ تم إرسال المستخدم: 02393317424
2025-07-15 01:56:08,855 - INFO - ✅ تم إرسال المستخدم: 02900559700
2025-07-15 01:56:08,895 - INFO - ✅ تم إرسال المستخدم: 02139755197
2025-07-15 01:56:08,936 - INFO - ✅ تم إرسال المستخدم: 02602521613
2025-07-15 01:56:08,975 - INFO - ✅ تم إرسال المستخدم: 02670422537
2025-07-15 01:56:09,015 - INFO - ✅ تم إرسال المستخدم: 02617382004
2025-07-15 01:56:09,062 - INFO - ✅ تم إرسال المستخدم: 02254655519
2025-07-15 01:56:09,114 - INFO - ✅ تم إرسال المستخدم: 02289595598
2025-07-15 01:56:09,157 - INFO - ✅ تم إرسال المستخدم: 02685011666
2025-07-15 01:56:09,213 - INFO - ✅ تم إرسال المستخدم: 02132025645
2025-07-15 01:56:09,263 - INFO - ✅ تم إرسال المستخدم: 02372766160
2025-07-15 01:56:09,318 - INFO - ✅ تم إرسال المستخدم: 02076394912
2025-07-15 01:56:09,360 - INFO - ✅ تم إرسال المستخدم: 02182824676
2025-07-15 01:56:09,398 - INFO - ✅ تم إرسال المستخدم: 02483539034
2025-07-15 01:56:09,458 - INFO - ✅ تم إرسال المستخدم: 02333910332
2025-07-15 01:56:09,517 - INFO - ✅ تم إرسال المستخدم: 02781484527
2025-07-15 01:56:09,592 - INFO - ✅ تم إرسال المستخدم: 02897605116
2025-07-15 01:56:09,640 - INFO - ✅ تم إرسال المستخدم: 02064523417
2025-07-15 01:56:09,701 - INFO - ✅ تم إرسال المستخدم: 02958119362
2025-07-15 01:56:09,762 - INFO - ✅ تم إرسال المستخدم: 02751550356
2025-07-15 01:56:09,826 - INFO - ✅ تم إرسال المستخدم: 02680034385
2025-07-15 01:56:09,887 - INFO - ✅ تم إرسال المستخدم: 02950104484
2025-07-15 01:56:09,948 - INFO - ✅ تم إرسال المستخدم: 02979542468
2025-07-15 01:56:10,011 - INFO - ✅ تم إرسال المستخدم: 02821182007
2025-07-15 01:56:10,071 - INFO - ✅ تم إرسال المستخدم: 02365710503
2025-07-15 01:56:10,120 - INFO - ✅ تم إرسال المستخدم: 02832812252
2025-07-15 01:56:10,198 - INFO - ✅ تم إرسال المستخدم: 02733350656
2025-07-15 01:56:10,238 - INFO - ✅ تم إرسال المستخدم: 02187152783
2025-07-15 01:56:10,279 - INFO - ✅ تم إرسال المستخدم: 02676942172
2025-07-15 01:56:10,315 - INFO - ✅ تم إرسال المستخدم: 02016822353
2025-07-15 01:56:10,367 - INFO - ✅ تم إرسال المستخدم: 02404565389
2025-07-15 01:56:10,427 - INFO - ✅ تم إرسال المستخدم: 02729706551
2025-07-15 01:56:10,477 - INFO - ✅ تم إرسال المستخدم: 02325121457
2025-07-15 01:56:10,527 - INFO - ✅ تم إرسال المستخدم: 02292800135
2025-07-15 01:56:10,574 - INFO - ✅ تم إرسال المستخدم: 02066139187
2025-07-15 01:56:10,616 - INFO - ✅ تم إرسال المستخدم: 02690058638
2025-07-15 01:56:10,673 - INFO - ✅ تم إرسال المستخدم: 02542311513
2025-07-15 01:56:10,740 - INFO - ✅ تم إرسال المستخدم: 02339284320
2025-07-15 01:56:10,785 - INFO - ✅ تم إرسال المستخدم: 02235595628
2025-07-15 01:56:10,835 - INFO - ✅ تم إرسال المستخدم: 02484374548
2025-07-15 01:56:10,879 - INFO - ✅ تم إرسال المستخدم: 02275087909
2025-07-15 01:56:10,928 - INFO - ✅ تم إرسال المستخدم: 02062297263
2025-07-15 01:56:10,985 - INFO - ✅ تم إرسال المستخدم: 02284081878
2025-07-15 01:56:11,036 - INFO - ✅ تم إرسال المستخدم: 02468488249
2025-07-15 01:56:11,088 - INFO - ✅ تم إرسال المستخدم: 02171642605
2025-07-15 01:56:11,136 - INFO - ✅ تم إرسال المستخدم: 02884006816
2025-07-15 01:56:11,182 - INFO - ✅ تم إرسال المستخدم: 02883228542
2025-07-15 01:56:11,243 - INFO - ✅ تم إرسال المستخدم: 02842808582
2025-07-15 01:56:11,298 - INFO - ✅ تم إرسال المستخدم: 02629169979
2025-07-15 01:56:11,355 - INFO - ✅ تم إرسال المستخدم: 02032348982
2025-07-15 01:56:11,396 - INFO - ✅ تم إرسال المستخدم: 02283155593
2025-07-15 01:56:11,435 - INFO - ✅ تم إرسال المستخدم: 02085127711
2025-07-15 01:56:11,475 - INFO - ✅ تم إرسال المستخدم: 02391471409
2025-07-15 01:56:11,515 - INFO - ✅ تم إرسال المستخدم: 02251411507
2025-07-15 01:56:11,556 - INFO - ✅ تم إرسال المستخدم: 02513969523
2025-07-15 01:56:11,598 - INFO - ✅ تم إرسال المستخدم: 02047230785
2025-07-15 01:56:11,635 - INFO - ✅ تم إرسال المستخدم: 02599906563
2025-07-15 01:56:11,675 - INFO - ✅ تم إرسال المستخدم: 02852758122
2025-07-15 01:56:11,726 - INFO - ✅ تم إرسال المستخدم: 02644279899
2025-07-15 01:56:11,770 - INFO - ✅ تم إرسال المستخدم: 02300885209
2025-07-15 01:56:11,807 - INFO - ✅ تم إرسال المستخدم: 02422701020
2025-07-15 01:56:11,847 - INFO - ✅ تم إرسال المستخدم: 02141818795
2025-07-15 01:56:11,885 - INFO - ✅ تم إرسال المستخدم: 02730800373
2025-07-15 01:56:11,933 - INFO - ✅ تم إرسال المستخدم: 02690670065
2025-07-15 01:56:11,975 - INFO - ✅ تم إرسال المستخدم: 02695873110
2025-07-15 01:56:12,015 - INFO - ✅ تم إرسال المستخدم: 02998965565
2025-07-15 01:56:12,065 - INFO - ✅ تم إرسال المستخدم: 02091441967
2025-07-15 01:56:12,105 - INFO - ✅ تم إرسال المستخدم: 02834862765
2025-07-15 01:56:12,148 - INFO - ✅ تم إرسال المستخدم: 02983082872
2025-07-15 01:56:12,186 - INFO - ✅ تم إرسال المستخدم: 02009239048
2025-07-15 01:56:12,225 - INFO - ✅ تم إرسال المستخدم: 02651462742
2025-07-15 01:56:12,277 - INFO - ✅ تم إرسال المستخدم: 02739798932
2025-07-15 01:56:12,315 - INFO - ✅ تم إرسال المستخدم: 02044110374
2025-07-15 01:56:12,364 - INFO - ✅ تم إرسال المستخدم: 02019346265
2025-07-15 01:56:12,420 - INFO - ✅ تم إرسال المستخدم: 02897279743
2025-07-15 01:56:12,466 - INFO - ✅ تم إرسال المستخدم: 02750081091
2025-07-15 01:56:12,528 - INFO - ✅ تم إرسال المستخدم: 02529782256
2025-07-15 01:56:12,568 - INFO - ✅ تم إرسال المستخدم: 02267389435
2025-07-15 01:56:12,605 - INFO - ✅ تم إرسال المستخدم: 02224078036
2025-07-15 01:56:12,645 - INFO - ✅ تم إرسال المستخدم: 02030395192
2025-07-15 01:56:12,687 - INFO - ✅ تم إرسال المستخدم: 02041002300
2025-07-15 01:56:12,732 - INFO - ✅ تم إرسال المستخدم: 02774732988
2025-07-15 01:56:12,790 - INFO - ✅ تم إرسال المستخدم: 02752903698
2025-07-15 01:56:12,839 - INFO - ✅ تم إرسال المستخدم: 02678027404
2025-07-15 01:56:12,885 - INFO - ✅ تم إرسال المستخدم: 02449966429
2025-07-15 01:56:12,939 - INFO - ✅ تم إرسال المستخدم: 02819473470
2025-07-15 01:56:12,994 - INFO - ✅ تم إرسال المستخدم: 02679598625
2025-07-15 01:56:13,043 - INFO - ✅ تم إرسال المستخدم: 02659295491
2025-07-15 01:56:13,094 - INFO - ✅ تم إرسال المستخدم: 02483447219
2025-07-15 01:56:13,152 - INFO - ✅ تم إرسال المستخدم: 02216897972
2025-07-15 01:56:13,207 - INFO - ✅ تم إرسال المستخدم: 02747048785
2025-07-15 01:56:13,261 - INFO - ✅ تم إرسال المستخدم: 02882819596
2025-07-15 01:56:13,323 - INFO - ✅ تم إرسال المستخدم: 02454374620
2025-07-15 01:56:13,324 - ERROR - ❌ كرت واحد: خطأ عام في الإرسال: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-15 01:56:13,327 - WARNING - ⚡ البرق الموحد: تم إنشاء 200 كارت وحفظ PDF، لكن فشل إرسال الكروت إلى MikroTik
2025-07-15 01:56:13,347 - INFO - تم إرسال 200 كرت عبر البرق الموحد باستخدام قالب 10
2025-07-15 01:58:18,670 - INFO - معالجة الأمر: /start
2025-07-15 01:58:18,920 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 01:58:20,249 - INFO - معالجة callback: select_system_um
2025-07-15 01:58:20,758 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-15 01:58:21,983 - INFO - معالجة callback: independent_template_um_10
2025-07-15 01:58:22,459 - INFO - 🔄 تبديل النظام تلقائياً من hotspot إلى user_manager
2025-07-15 01:58:22,459 - INFO - 🔄 طلب تبديل النظام من التلجرام: hotspot → user_manager
2025-07-15 01:58:22,708 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-15 01:58:22,708 - INFO - ✅ تم قطع الاتصال الموجود بنجاح
2025-07-15 01:58:22,709 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-15 01:58:22,816 - INFO - تم حفظ الإعدادات قبل التبديل
2025-07-15 01:58:22,817 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-15 01:58:22,817 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-15 01:58:22,818 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-15 01:58:22,819 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-15 01:58:23,176 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-15 01:58:23,201 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 01:58:23,203 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 01:58:23,412 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-15 01:58:23,412 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-15 01:58:23,495 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-15 01:58:23,501 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من hotspot إلى user_manager
2025-07-15 01:58:23,534 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-15 01:58:23,535 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 01:58:23,535 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 01:58:23,671 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 01:58:23,687 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 01:58:23,769 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-15 01:58:23,769 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 01:58:23,772 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 01:58:23,775 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 01:58:23,780 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 01:58:23,793 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-15 01:58:24,090 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-15 01:58:24,229 - INFO - تم تفعيل إشعارات Telegram للعمليات والأخطاء
2025-07-15 01:58:32,652 - INFO - محاولة الاتصال بـ ***********:8729 (SSL)
2025-07-15 01:58:32,925 - INFO - نجح الاتصال مع ***********
2025-07-15 01:58:34,897 - INFO - تم جلب 3 بروفايل يدوياً من النظام: user_manager
2025-07-15 01:59:01,932 - INFO - 🔄 تغيير إعدادات SSL - قطع أي اتصالات موجودة...
2025-07-15 01:59:01,932 - INFO - ✅ تم قطع الاتصال الموجود بنجاح
2025-07-15 01:59:01,933 - INFO - ✅ تغيير إعدادات SSL - تم تنظيف حالة الاتصال بنجاح
2025-07-15 01:59:02,034 - INFO - تم إلغاء تفعيل SSL - المنفذ الافتراضي: 8728
2025-07-15 01:59:04,012 - INFO - بدء اختبار الاتصال مع MikroTik
2025-07-15 01:59:04,015 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-15 01:59:04,119 - INFO - نجح الاتصال مع *********
2025-07-15 01:59:05,983 - INFO - تم تسجيل العملية: اختبار الاتصال - نجح الاتصال مع MikroTik
2025-07-15 01:59:09,417 - INFO - تم حفظ القالب: 10
2025-07-15 01:59:09,933 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 01:59:09,936 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 01:59:10,256 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 01:59:10,256 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 01:59:11,082 - INFO - تم حفظ القالب: 10
2025-07-15 01:59:11,587 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 01:59:11,589 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 01:59:11,843 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 01:59:11,843 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 01:59:11,847 - INFO - تم حفظ القالب: 10
2025-07-15 01:59:12,353 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 01:59:12,356 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 01:59:12,607 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 01:59:12,607 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 01:59:16,042 - INFO - تم حفظ القالب: 10
2025-07-15 01:59:16,547 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 01:59:16,550 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 01:59:16,794 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 01:59:16,795 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 01:59:52,120 - INFO - معالجة callback: select_system_um
2025-07-15 01:59:52,675 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-15 01:59:54,144 - INFO - معالجة callback: independent_template_um_10
2025-07-15 01:59:54,651 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-15 01:59:54,652 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 01:59:54,652 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 01:59:54,668 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 01:59:54,668 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 01:59:54,690 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-15 01:59:54,692 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 01:59:54,695 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 01:59:54,701 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 01:59:54,705 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 01:59:54,706 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-15 01:59:54,989 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-15 02:00:00,075 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-15 02:00:03,489 - INFO - معالجة callback: independent_custom_um_10_lightning
2025-07-15 02:00:16,932 - INFO - معالجة الأمر: 105
2025-07-15 02:00:17,307 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 02:00:17,307 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 02:00:17,323 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 02:00:17,325 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 02:00:17,347 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-15 02:00:17,350 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 02:00:17,353 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 02:00:17,357 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 02:00:17,620 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 02:00:17,620 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 02:00:17,635 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 02:00:17,636 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 02:00:17,656 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-15 02:00:17,656 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 02:00:17,659 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 02:00:17,667 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 02:00:17,669 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 1801
2025-07-15 02:00:17,670 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-15 02:00:17,670 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-15 02:00:17,670 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-15 02:00:17,670 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 105
2025-07-15 02:00:17,671 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 105 حساب
2025-07-15 02:00:17,671 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 105
2025-07-15 02:00:17,673 - INFO - ⚡ البرق للتلجرام: تم توليد 105 حساب
2025-07-15 02:00:17,673 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 105 كارت (حد التقسيم: 100)
2025-07-15 02:00:17,674 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-15 02:00:17,674 - INFO - ⚡ البرق للتلجرام: تم تقسيم 105 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-15 02:00:17,685 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-15 02:00:17,685 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250715_020017
2025-07-15 02:00:17,686 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (5 كارت)
2025-07-15 02:00:17,686 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250715_020017
2025-07-15 02:00:17,689 - INFO - استخدام الاتصال الحالي
2025-07-15 02:00:17,734 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250715_020017
2025-07-15 02:00:17,776 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250715_020017
2025-07-15 02:00:17,818 - INFO - ⚡ وقت MikroTik الحالي: 23:00:14
2025-07-15 02:00:17,860 - INFO - ⚡ السكريبت الأول سينفذ في: 23:00:17 (بعد 3 ثواني من وقت MikroTik)
2025-07-15 02:00:17,910 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250715_020017 لتشغيل telegram_lightning_batch1_user_manager_20250715_020017 في 23:00:17
2025-07-15 02:00:17,913 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250715_020017, 2 سكريبت مترابط
2025-07-15 02:00:17,913 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (105 كارت)
2025-07-15 02:00:20,739 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 105
2025-07-15 02:00:20,758 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-105 كارت-15-07-2025-02-00-20-um.pdf
2025-07-15 02:00:20,774 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-105 كارت-15-07-2025-02-00-20-um.rsc
2025-07-15 02:00:21,418 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-105 كارت-15-07-2025-02-00-20-um.pdf
2025-07-15 02:00:21,664 - INFO - تم إرسال 105 كرت عبر التلجرام باستخدام قالب 10
2025-07-15 02:06:41,704 - INFO - تم بدء تشغيل التطبيق
2025-07-15 02:06:41,713 - INFO - تم إنشاء المجلدات الأساسية
2025-07-15 02:06:41,719 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-15 02:06:41,738 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-15 02:06:41,843 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250715_020641.db
2025-07-15 02:06:41,846 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-15 02:06:42,222 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-15 02:06:42,223 - INFO - تم إعداد التطبيق بنجاح
2025-07-15 02:06:44,225 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-15 02:06:44,571 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-15 02:06:44,572 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-15 02:06:47,586 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 02:06:47,587 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 02:06:47,870 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 02:06:47,871 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 02:06:52,762 - INFO - معالجة الأمر: /start
2025-07-15 02:06:53,031 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 02:06:55,170 - INFO - معالجة callback: select_system_um
2025-07-15 02:06:55,637 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-15 02:06:58,375 - INFO - معالجة callback: independent_template_um_10
2025-07-15 02:06:59,013 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-15 02:06:59,014 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-15 02:06:59,287 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-15 02:06:59,787 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-15 02:06:59,889 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-15 02:06:59,890 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-15 02:06:59,890 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-15 02:06:59,891 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-15 02:06:59,893 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-15 02:07:00,295 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-15 02:07:00,466 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 02:07:00,466 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 02:07:00,594 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-15 02:07:00,595 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-15 02:07:00,656 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-15 02:07:00,668 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-15 02:07:00,712 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-15 02:07:00,713 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 02:07:00,713 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 02:07:00,866 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 02:07:00,926 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 02:07:00,949 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-15 02:07:00,953 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 02:07:00,956 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 02:07:00,962 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 02:07:00,964 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 02:07:00,966 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-15 02:07:01,244 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-15 02:07:01,467 - INFO - تم تفعيل إشعارات Telegram للعمليات والأخطاء
2025-07-15 02:07:08,140 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-15 02:07:20,421 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-15 02:07:20,964 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 02:07:20,964 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 02:07:20,975 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 02:07:20,979 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 02:07:21,000 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-15 02:07:21,001 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 02:07:21,003 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 02:07:21,007 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 02:07:21,248 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 02:07:21,248 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 02:07:21,254 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 02:07:21,261 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 02:07:21,280 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-15 02:07:21,281 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 02:07:21,283 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 02:07:21,292 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 02:07:21,294 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 1801
2025-07-15 02:07:21,294 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-15 02:07:21,294 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-15 02:07:21,295 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-15 02:07:21,295 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-15 02:07:21,295 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-15 02:07:21,295 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-15 02:07:21,299 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-15 02:07:21,303 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-15 02:07:21,303 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-15 02:07:21,304 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-15 02:07:21,304 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-15 02:07:21,305 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250715_020721
2025-07-15 02:07:21,305 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-15 02:07:21,305 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250715_020721
2025-07-15 02:07:21,305 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-15 02:07:21,379 - INFO - نجح الاتصال مع *********
2025-07-15 02:07:21,431 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250715_020721
2025-07-15 02:07:21,470 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250715_020721
2025-07-15 02:07:21,519 - INFO - ⚡ وقت MikroTik الحالي: 23:07:18
2025-07-15 02:07:21,530 - INFO - ⚡ السكريبت الأول سينفذ في: 23:07:21 (بعد 3 ثواني من وقت MikroTik)
2025-07-15 02:07:21,582 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250715_020721 لتشغيل telegram_lightning_batch1_user_manager_20250715_020721 في 23:07:21
2025-07-15 02:07:21,585 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250715_020721, 2 سكريبت مترابط
2025-07-15 02:07:21,585 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-15 02:07:24,429 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-15 02:07:24,449 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-15-07-2025-02-07-24-um.pdf
2025-07-15 02:07:24,468 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-15-07-2025-02-07-24-um.rsc
2025-07-15 02:07:25,047 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-15-07-2025-02-07-24-um.pdf
2025-07-15 02:07:25,299 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-15 02:08:39,324 - INFO - بدء إغلاق التطبيق
2025-07-15 02:08:39,327 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250715_020839.db
2025-07-15 02:08:39,328 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250715_020839.db
2025-07-15 02:08:39,328 - INFO - تم قطع الاتصال مع MikroTik
2025-07-15 02:08:39,333 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-15 02:08:39,334 - INFO - تم إغلاق التطبيق بنجاح
2025-07-15 02:39:37,077 - INFO - تم بدء تشغيل التطبيق
2025-07-15 02:39:37,110 - INFO - تم إنشاء المجلدات الأساسية
2025-07-15 02:39:37,242 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-15 02:39:37,326 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-15 02:39:38,283 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-15 02:39:39,912 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-15 02:39:39,915 - INFO - تم إعداد التطبيق بنجاح
2025-07-15 02:39:41,928 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-15 02:39:42,391 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-15 02:39:42,391 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-15 02:39:45,396 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 02:39:45,421 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 02:39:45,752 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 02:39:45,752 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 02:39:57,290 - INFO - معالجة callback: select_system_um
2025-07-15 02:39:57,747 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-15 02:40:13,426 - INFO - معالجة callback: independent_template_um_10
2025-07-15 02:40:14,008 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-15 02:40:14,010 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-15 02:40:14,348 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-15 02:40:14,468 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-15 02:40:14,569 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-15 02:40:14,570 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-15 02:40:14,570 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-15 02:40:14,571 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-15 02:40:14,574 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-15 02:40:15,219 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-15 02:40:15,435 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 02:40:15,437 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 02:40:15,678 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-15 02:40:15,679 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-15 02:40:15,809 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-15 02:40:15,818 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-15 02:40:15,820 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-15 02:40:15,820 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 02:40:15,820 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 02:40:16,150 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 02:40:16,152 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 02:40:16,261 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-15 02:40:16,262 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 02:40:16,267 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 02:40:16,286 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 02:40:16,301 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 02:40:16,304 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-15 02:40:16,700 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-15 02:40:24,049 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-15 02:40:31,934 - INFO - بدء إغلاق التطبيق
2025-07-15 02:40:31,939 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-15 02:40:31,939 - INFO - تم إغلاق التطبيق بنجاح
2025-07-15 02:40:40,730 - INFO - تم بدء تشغيل التطبيق
2025-07-15 02:40:40,802 - INFO - تم إنشاء المجلدات الأساسية
2025-07-15 02:40:40,805 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-15 02:40:40,810 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-15 02:40:40,913 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-15 02:40:41,471 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-15 02:40:41,472 - INFO - تم إعداد التطبيق بنجاح
2025-07-15 02:40:43,472 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-15 02:40:43,745 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-15 02:40:43,766 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-15 02:40:46,778 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 02:40:46,780 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 02:40:47,019 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 02:40:47,019 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 02:40:50,721 - INFO - معالجة callback: select_system_um
2025-07-15 02:40:51,207 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-15 02:40:51,816 - INFO - تم اختيار النظام: user_manager
2025-07-15 02:40:52,117 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-15 02:40:52,279 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 02:40:52,280 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 02:40:52,399 - INFO - تم تسجيل العملية: اختيار النظام - تم اختيار نظام user_manager
2025-07-15 02:40:52,550 - INFO - معالجة callback: independent_template_um_10
2025-07-15 02:40:53,052 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-15 02:40:53,053 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 02:40:53,053 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 02:40:53,068 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 02:40:53,069 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 02:40:53,096 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-15 02:40:53,113 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 02:40:53,116 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 02:40:53,150 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 02:40:53,179 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 02:40:53,181 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-15 02:40:53,480 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-15 02:40:57,946 - INFO - بدء إغلاق التطبيق
2025-07-15 02:40:57,955 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-15 02:40:57,990 - INFO - تم إغلاق التطبيق بنجاح
2025-07-15 02:41:08,220 - INFO - تم بدء تشغيل التطبيق
2025-07-15 02:41:08,318 - INFO - تم إنشاء المجلدات الأساسية
2025-07-15 02:41:08,320 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-15 02:41:08,320 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-15 02:41:08,423 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-15 02:41:08,963 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-15 02:41:08,963 - INFO - تم إعداد التطبيق بنجاح
2025-07-15 02:41:10,981 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-15 02:41:11,213 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-15 02:41:11,214 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-15 02:41:14,225 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 02:41:14,230 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 02:41:14,470 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 02:41:14,471 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 02:41:19,461 - INFO - معالجة callback: select_system_um
2025-07-15 02:41:19,934 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-15 02:41:22,732 - INFO - معالجة callback: independent_template_um_10
2025-07-15 02:41:23,243 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-15 02:41:23,243 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-15 02:41:23,516 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-15 02:41:23,569 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-15 02:41:23,669 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-15 02:41:23,670 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-15 02:41:23,709 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-15 02:41:23,719 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-15 02:41:23,724 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-15 02:41:24,181 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-15 02:41:24,412 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 02:41:24,413 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 02:41:24,537 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-15 02:41:24,538 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-15 02:41:24,692 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-15 02:41:24,696 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-15 02:41:24,797 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-15 02:41:24,798 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 02:41:24,799 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 02:41:25,058 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 02:41:25,062 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 02:41:25,128 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-15 02:41:25,154 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 02:41:25,158 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 02:41:25,164 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 02:41:25,194 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 02:41:25,196 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-15 02:41:25,482 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-15 02:41:32,785 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-15 02:41:36,544 - INFO - معالجة callback: independent_count_um_10_lightning_200
2025-07-15 02:41:37,027 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 02:41:37,028 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 02:41:37,067 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 02:41:37,067 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 02:41:37,178 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-15 02:41:37,178 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 02:41:37,182 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 02:41:37,186 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 02:41:37,422 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 02:41:37,422 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 02:41:37,437 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 02:41:37,437 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 02:41:37,518 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-15 02:41:37,519 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 02:41:37,523 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 02:41:37,554 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 02:41:37,557 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 1801
2025-07-15 02:41:37,590 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-15 02:41:37,591 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-15 02:41:37,591 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-15 02:41:37,591 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 200
2025-07-15 02:41:37,592 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 200 حساب
2025-07-15 02:41:37,592 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 200
2025-07-15 02:41:37,598 - INFO - ⚡ البرق للتلجرام: تم توليد 200 حساب
2025-07-15 02:41:37,599 - INFO - ⚡ البرق للتلجرام: تنفيذ متسلسل مجدول لـ 200 كارت (حد التقسيم: 100)
2025-07-15 02:41:37,600 - INFO - ⚡ البرق للتلجرام: بدء معالجة الأعداد الكبيرة مع جدولة واحدة
2025-07-15 02:41:37,600 - INFO - ⚡ البرق للتلجرام: تم تقسيم 200 كارت إلى 2 مجموعة للتنفيذ المتسلسل
2025-07-15 02:41:37,601 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 1/2 (100 كارت)
2025-07-15 02:41:37,601 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch1_user_manager_20250715_024137
2025-07-15 02:41:37,601 - INFO - ⚡ البرق للتلجرام: إنشاء السكريبت للمجموعة 2/2 (100 كارت)
2025-07-15 02:41:37,602 - INFO - ⚡ البرق للتلجرام: تم إعداد السكريبت telegram_lightning_batch2_user_manager_20250715_024137
2025-07-15 02:41:37,602 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-15 02:41:37,664 - INFO - نجح الاتصال مع *********
2025-07-15 02:41:37,740 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch1_user_manager_20250715_024137
2025-07-15 02:41:37,781 - INFO - ⚡ تم إرسال السكريبت المترابط: telegram_lightning_batch2_user_manager_20250715_024137
2025-07-15 02:41:37,826 - INFO - ⚡ وقت MikroTik الحالي: 23:41:34
2025-07-15 02:41:37,861 - INFO - ⚡ السكريبت الأول سينفذ في: 23:41:37 (بعد 3 ثواني من وقت MikroTik)
2025-07-15 02:41:37,926 - INFO - ⚡ تم إنشاء الجدولة الرئيسية: telegram_main_schedule_20250715_024137 لتشغيل telegram_lightning_batch1_user_manager_20250715_024137 في 23:41:37
2025-07-15 02:41:37,933 - INFO - ⚡ تم إعداد البرق المترابط بنجاح - جدولة واحدة: telegram_main_schedule_20250715_024137, 2 سكريبت مترابط
2025-07-15 02:41:37,936 - INFO - ⚡ البرق للتلجرام مكتمل: تم إنشاء جدولة واحدة لـ 2 مجموعة متسلسلة (200 كارت)
2025-07-15 02:41:40,807 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 200
2025-07-15 02:41:40,867 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-200 كارت-15-07-2025-02-41-40-um.pdf
2025-07-15 02:41:40,872 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-200 كارت-15-07-2025-02-41-40-um.rsc
2025-07-15 02:41:41,797 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-200 كارت-15-07-2025-02-41-40-um.pdf
2025-07-15 02:41:42,058 - INFO - تم إرسال 200 كرت عبر التلجرام باستخدام قالب 10
2025-07-15 02:44:08,652 - INFO - بدء إغلاق التطبيق
2025-07-15 02:44:08,661 - INFO - تم قطع الاتصال مع MikroTik
2025-07-15 02:44:08,725 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-15 02:44:08,731 - INFO - تم إغلاق التطبيق بنجاح
2025-07-15 03:04:39,928 - INFO - تم بدء تشغيل التطبيق
2025-07-15 03:04:40,328 - INFO - تم إنشاء المجلدات الأساسية
2025-07-15 03:04:40,330 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-15 03:04:40,334 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-15 03:04:40,438 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-15 03:04:40,818 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-15 03:04:40,819 - INFO - تم إعداد التطبيق بنجاح
2025-07-15 03:04:42,823 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-15 03:04:43,107 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-15 03:04:43,108 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-15 03:04:44,256 - INFO - تم اختيار النظام: user_manager
2025-07-15 03:04:44,543 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-15 03:04:44,702 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 03:04:44,703 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 03:04:44,821 - INFO - تم تسجيل العملية: اختيار النظام - تم اختيار نظام user_manager
2025-07-15 03:04:46,108 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 03:04:46,109 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 03:04:46,453 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 03:04:46,453 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 03:05:37,974 - INFO - معالجة callback: select_system_um
2025-07-15 03:05:38,431 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-15 03:05:39,862 - INFO - معالجة callback: independent_template_um_10
2025-07-15 03:05:40,324 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-15 03:05:40,325 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 03:05:40,327 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 03:05:40,332 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 03:05:40,332 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 03:05:40,354 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 68 إعدادات
2025-07-15 03:05:40,354 - INFO - ✅ تم تطبيق 68 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 03:05:40,359 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 03:05:40,368 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 03:05:40,369 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 03:05:40,369 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-15 03:05:40,691 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-15 03:07:04,037 - INFO - تم نسخ صورة الخلفية إلى: img\100100_1.jpg
2025-07-15 03:07:12,728 - INFO - تم حفظ القالب: 10
2025-07-15 03:07:13,232 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 03:07:13,233 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 03:07:13,511 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 03:07:13,512 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 03:07:15,370 - INFO - بدء إغلاق التطبيق
2025-07-15 03:07:15,372 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250715_030715.db
2025-07-15 03:07:15,373 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250715_030715.db
2025-07-15 03:07:15,377 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-15 03:07:15,377 - INFO - تم إغلاق التطبيق بنجاح
2025-07-15 03:53:17,414 - INFO - تم بدء تشغيل التطبيق
2025-07-15 03:53:17,525 - INFO - تم إنشاء المجلدات الأساسية
2025-07-15 03:53:17,529 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-15 03:53:17,580 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-15 03:53:17,683 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-15 03:53:18,302 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-15 03:53:18,303 - INFO - تم إعداد التطبيق بنجاح
2025-07-15 03:53:20,316 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-15 03:53:20,592 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-15 03:53:20,593 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-15 03:53:23,601 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 03:53:23,644 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 03:53:23,943 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 03:53:23,944 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 03:54:11,463 - INFO - تم اختيار النظام: user_manager
2025-07-15 03:54:11,923 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-15 03:54:12,122 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 03:54:12,130 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 03:54:12,888 - INFO - تم تسجيل العملية: اختيار النظام - تم اختيار نظام user_manager
2025-07-15 03:54:42,260 - INFO - بدء إغلاق التطبيق
2025-07-15 03:54:42,290 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-15 03:54:42,313 - INFO - تم إغلاق التطبيق بنجاح
2025-07-15 17:19:05,372 - INFO - تم بدء تشغيل التطبيق
2025-07-15 17:19:05,473 - INFO - تم إنشاء المجلدات الأساسية
2025-07-15 17:19:05,483 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-15 17:19:05,483 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-15 17:19:05,704 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-15 17:19:07,195 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-15 17:19:07,205 - INFO - تم إعداد التطبيق بنجاح
2025-07-15 17:19:09,227 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-15 17:19:09,573 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-15 17:19:09,574 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-15 17:19:12,576 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 17:19:12,599 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 17:19:12,909 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 17:19:12,909 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 17:19:27,896 - INFO - معالجة callback: single_card
2025-07-15 17:19:28,109 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-15 17:19:28,110 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-15 17:19:28,395 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-15 17:19:53,730 - INFO - معالجة callback: select_system_hs
2025-07-15 17:19:54,211 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-15 17:20:00,322 - INFO - معالجة callback: independent_template_hs_10
2025-07-15 17:20:00,842 - INFO - 🔄 تبديل النظام تلقائياً من None إلى hotspot
2025-07-15 17:20:00,843 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → hotspot
2025-07-15 17:20:01,092 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-15 17:20:01,193 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-15 17:20:01,294 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-15 17:20:01,294 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-15 17:20:01,295 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-15 17:20:01,296 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-15 17:20:01,302 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-15 17:20:01,805 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-15 17:20:01,952 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 17:20:01,953 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 17:20:02,686 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-15 17:20:02,687 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-15 17:20:02,754 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-15 17:20:02,780 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى hotspot
2025-07-15 17:20:02,791 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-15 17:20:02,844 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 17:20:02,957 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 17:20:03,036 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 17:20:03,043 - INFO - ✅ تم تحديث Host: ********* → ***********
2025-07-15 17:20:03,045 - INFO - ✅ تم تحديث Username: 11 → saye
2025-07-15 17:20:03,047 - INFO - ✅ تم تحديث Password
2025-07-15 17:20:03,048 - INFO - ✅ تم تحديث Port: 8728 → 8729
2025-07-15 17:20:03,049 - INFO - ✅ تم تحديث SSL: مفعل
2025-07-15 17:20:03,050 - INFO - 🔄 تم جدولة تحديث معلومات الخادم في البوت
2025-07-15 17:20:03,050 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 5 إعدادات
2025-07-15 17:20:03,076 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 77 إعدادات
2025-07-15 17:20:03,076 - INFO - ✅ تم تطبيق 77 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 17:20:03,080 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 17:20:03,083 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 17:20:03,088 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 17:20:03,091 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 17:20:03,352 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-15 17:20:31,735 - INFO - بدء اختبار الاتصال مع MikroTik
2025-07-15 17:20:31,735 - INFO - محاولة الاتصال بـ *********:8729 (SSL)
2025-07-15 17:20:31,801 - ERROR - خطأ في الاتصال - النوع: SSLError, الرسالة: [SSL: SSLV3_ALERT_HANDSHAKE_FAILURE] sslv3 alert handshake failure (_ssl.c:1028)
2025-07-15 17:20:31,802 - INFO - تم قطع الاتصال مع MikroTik
2025-07-15 17:20:34,627 - INFO - تم تسجيل العملية: اختبار الاتصال - فشل الاتصال
2025-07-15 17:20:36,745 - INFO - 🔄 تغيير إعدادات SSL - قطع أي اتصالات موجودة...
2025-07-15 17:20:36,746 - INFO - ✅ تغيير إعدادات SSL - تم تنظيف حالة الاتصال بنجاح
2025-07-15 17:20:36,846 - INFO - تم إلغاء تفعيل SSL - المنفذ الافتراضي: 8728
2025-07-15 17:20:38,903 - INFO - تم حفظ القالب: 10
2025-07-15 17:20:39,417 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 17:20:39,419 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 17:20:39,725 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 17:20:39,725 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 17:20:41,294 - INFO - بدء اختبار الاتصال مع MikroTik
2025-07-15 17:20:41,294 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-15 17:20:41,389 - INFO - نجح الاتصال مع *********
2025-07-15 17:20:43,608 - INFO - تم تسجيل العملية: اختبار الاتصال - نجح الاتصال مع MikroTik
2025-07-15 17:20:47,149 - INFO - معالجة callback: single_card
2025-07-15 17:20:47,387 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-15 17:20:47,388 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-15 17:20:47,640 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-15 17:20:48,901 - INFO - معالجة callback: card_count_1
2025-07-15 17:20:49,186 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-15 17:20:49,450 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-15 17:20:55,722 - INFO - معالجة callback: cards_template_1_10
2025-07-15 17:20:55,935 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 17:20:56,195 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-15 17:20:56,196 - INFO - النظام hotspot مفعل بالفعل
2025-07-15 17:20:56,197 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-15 17:20:56,197 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-15 17:20:56,245 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 17:20:56,271 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 17:20:56,275 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 17:20:56,296 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 17:20:56,309 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 17:20:56,337 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 17:20:56,338 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 17:20:56,341 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 17:20:56,347 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 17:20:56,347 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 17:20:56,348 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 17:20:56,352 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 17:20:56,367 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 17:20:56,371 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 17:20:56,373 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 17:20:56,373 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 17:20:56,375 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 17:20:56,375 - INFO - 🏭 بدء توليد الكرت...
2025-07-15 17:20:56,468 - INFO - تم توليد 1 حساب
2025-07-15 17:20:56,469 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 201 (من generate_all)
2025-07-15 17:20:56,501 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-15 17:20:56,508 - INFO - استخدام الاتصال الحالي
2025-07-15 17:20:56,794 - INFO - ✅ تم إرسال المستخدم: 02905491719
2025-07-15 17:20:56,794 - ERROR - ❌ كرت واحد: خطأ عام في الإرسال: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-15 17:20:56,794 - WARNING - ⚠️ فشل في الإرسال التلقائي، لكن الكرت تم توليده
2025-07-15 17:20:57,067 - INFO - ✅ تم إرسال تفاصيل 1 كرت عبر التلجرام بنجاح
2025-07-15 17:21:23,245 - INFO - معالجة الأمر: /restart
2025-07-15 17:21:23,246 - INFO - طلب إعادة تشغيل البرنامج من التلجرام
2025-07-15 17:21:24,757 - INFO - معالجة callback: confirm_restart
2025-07-15 17:21:25,014 - INFO - تم تأكيد إعادة تشغيل البرنامج من التلجرام
2025-07-15 17:21:25,014 - INFO - بدء عملية إعادة تشغيل البرنامج من التلجرام
2025-07-15 17:21:30,786 - INFO - تم تسجيل العملية: إعادة تشغيل من التلجرام - بدء عملية إعادة التشغيل
2025-07-15 17:21:36,226 - INFO - تم بدء تشغيل التطبيق
2025-07-15 17:21:36,234 - INFO - تم إنشاء المجلدات الأساسية
2025-07-15 17:21:36,235 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-15 17:21:36,235 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-15 17:21:36,386 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250715_172136.db
2025-07-15 17:21:36,389 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-15 17:21:36,754 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-15 17:21:36,755 - INFO - تم إعداد التطبيق بنجاح
2025-07-15 17:21:38,759 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-15 17:21:39,084 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-15 17:21:39,089 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-15 17:21:42,109 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 17:21:42,122 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 17:21:42,374 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 17:21:42,374 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 17:21:58,457 - INFO - معالجة callback: single_card
2025-07-15 17:21:58,751 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-15 17:21:58,752 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-15 17:21:59,138 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-15 17:22:00,684 - INFO - معالجة callback: card_count_1
2025-07-15 17:22:00,897 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-15 17:22:01,141 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-15 17:22:02,114 - INFO - معالجة callback: cards_template_1_10
2025-07-15 17:22:02,341 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 17:22:02,601 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-15 17:22:02,603 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-15 17:22:02,615 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-15 17:22:02,716 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-15 17:22:02,717 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-15 17:22:02,717 - INFO - ✅ تم تبديل النظام من None إلى hotspot
2025-07-15 17:22:02,717 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-15 17:22:02,719 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-15 17:22:02,724 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-15 17:22:03,194 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-15 17:22:03,335 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 17:22:03,335 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 17:22:03,471 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-15 17:22:03,472 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-15 17:22:03,473 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-15 17:22:03,749 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 17:22:03,753 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 17:22:03,754 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 17:22:03,825 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 17:22:03,838 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 17:22:03,863 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 17:22:03,874 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 17:22:03,877 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 17:22:03,881 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 17:22:03,882 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 17:22:03,882 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 17:22:03,885 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 17:22:03,894 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 17:22:03,899 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 17:22:03,902 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 17:22:03,902 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 17:22:03,903 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 17:22:03,903 - INFO - 🏭 بدء توليد الكرت...
2025-07-15 17:22:03,910 - INFO - تم توليد 1 حساب
2025-07-15 17:22:03,911 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 202 (من generate_all)
2025-07-15 17:22:03,914 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-15 17:22:03,927 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-15 17:22:04,016 - INFO - نجح الاتصال مع *********
2025-07-15 17:22:04,239 - INFO - ✅ تم إرسال المستخدم: 02710431739
2025-07-15 17:22:04,240 - ERROR - ❌ كرت واحد: خطأ عام في الإرسال: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-15 17:22:04,240 - WARNING - ⚠️ فشل في الإرسال التلقائي، لكن الكرت تم توليده
2025-07-15 17:22:04,502 - INFO - ✅ تم إرسال تفاصيل 1 كرت عبر التلجرام بنجاح
2025-07-15 17:22:17,551 - INFO - معالجة الأمر: /start
2025-07-15 17:22:17,864 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 17:22:19,126 - INFO - معالجة callback: select_system_um
2025-07-15 17:22:19,619 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-15 17:22:20,910 - INFO - معالجة callback: independent_template_um_10
2025-07-15 17:22:21,379 - INFO - 🔄 تبديل النظام تلقائياً من hotspot إلى user_manager
2025-07-15 17:22:21,379 - INFO - 🔄 طلب تبديل النظام من التلجرام: hotspot → user_manager
2025-07-15 17:22:21,673 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-15 17:22:21,673 - INFO - ✅ تم قطع الاتصال الموجود بنجاح
2025-07-15 17:22:21,674 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-15 17:22:21,778 - INFO - تم حفظ الإعدادات قبل التبديل
2025-07-15 17:22:21,779 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-15 17:22:21,780 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-15 17:22:21,781 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-15 17:22:21,783 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-15 17:22:22,064 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-15 17:22:22,088 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 17:22:22,089 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 17:22:22,223 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-15 17:22:22,224 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-15 17:22:22,247 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-15 17:22:22,260 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من hotspot إلى user_manager
2025-07-15 17:22:22,265 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-15 17:22:22,297 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 17:22:22,297 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 17:22:22,407 - INFO - 🖼️ محاولة تحميل صورة الخلفية: img\100100_1.jpg
2025-07-15 17:22:22,463 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-15 17:22:22,481 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-15 17:22:22,497 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 17:22:22,498 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 17:22:22,602 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-15 17:22:22,607 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 17:22:22,610 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 17:22:22,620 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 17:22:22,624 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 17:22:22,625 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-15 17:22:22,964 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-15 17:22:23,109 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-15 17:22:23,111 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح
2025-07-15 17:22:23,236 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-15 17:22:23,238 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح
2025-07-15 17:22:34,777 - INFO - معالجة الأمر: /start
2025-07-15 17:22:35,019 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 17:22:36,433 - INFO - معالجة callback: single_card
2025-07-15 17:22:36,653 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-15 17:22:36,654 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-15 17:22:36,913 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-15 17:22:38,333 - INFO - معالجة callback: card_count_1
2025-07-15 17:22:38,591 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-15 17:22:38,848 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-15 17:22:39,737 - INFO - معالجة callback: cards_template_1_10
2025-07-15 17:22:39,971 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 17:22:40,439 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-15 17:22:40,441 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-15 17:22:40,442 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-15 17:22:40,550 - INFO - تم حفظ الإعدادات قبل التبديل
2025-07-15 17:22:40,550 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-15 17:22:40,550 - INFO - ✅ تم تبديل النظام من user_manager إلى hotspot
2025-07-15 17:22:40,550 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-15 17:22:40,551 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-15 17:22:40,551 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-15 17:22:41,404 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-15 17:22:41,404 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 17:22:41,404 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 17:22:41,519 - ERROR - خطأ في إعادة بناء الواجهة: invalid command name ".!canvas2.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 17:22:41,535 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-15 17:22:41,537 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-15 17:22:41,714 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 17:22:41,729 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 17:22:41,729 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 17:22:41,796 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 17:22:41,810 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 17:22:41,943 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 17:22:42,008 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 17:22:42,012 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 17:22:42,019 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 17:22:42,020 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 17:22:42,020 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 17:22:42,023 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 17:22:42,033 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 17:22:42,039 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 17:22:42,040 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 17:22:42,041 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 17:22:42,041 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 17:22:42,055 - INFO - 🏭 بدء توليد الكرت...
2025-07-15 17:22:42,072 - INFO - تم توليد 1 حساب
2025-07-15 17:22:42,073 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 203 (من generate_all)
2025-07-15 17:22:42,078 - ERROR - ❌ خطأ في تنفيذ إنشاء كرت واحد: invalid command name ".!canvas2.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 17:23:01,913 - INFO - بدء إغلاق التطبيق
2025-07-15 17:23:01,915 - ERROR - خطأ أثناء إغلاق التطبيق: invalid command name ".!canvas2.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 19:04:10,637 - INFO - تم بدء تشغيل التطبيق
2025-07-15 19:04:10,756 - INFO - تم إنشاء المجلدات الأساسية
2025-07-15 19:04:10,952 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-15 19:04:10,952 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:04:11,892 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-15 19:04:16,962 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-15 19:04:16,963 - INFO - تم إعداد التطبيق بنجاح
2025-07-15 19:04:18,997 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-15 19:04:19,383 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-15 19:04:19,392 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-15 19:04:22,398 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 19:04:22,446 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 19:04:22,819 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 19:04:22,819 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 19:07:17,133 - INFO - معالجة callback: select_system_um
2025-07-15 19:07:17,934 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-15 19:07:19,179 - INFO - معالجة callback: independent_template_um_10
2025-07-15 19:07:19,681 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-15 19:07:19,681 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-15 19:07:19,933 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-15 19:07:20,028 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:07:20,129 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-15 19:07:20,129 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-15 19:07:20,130 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-15 19:07:20,131 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-15 19:07:20,139 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:07:20,725 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-15 19:07:20,883 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 19:07:20,884 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:07:21,661 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-15 19:07:21,678 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-15 19:07:21,746 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-15 19:07:21,781 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-15 19:07:21,809 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-15 19:07:21,811 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 19:07:21,811 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 19:07:22,190 - INFO - 🖼️ محاولة تحميل صورة الخلفية: img\100100_1.jpg
2025-07-15 19:07:22,191 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-15 19:07:22,192 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-15 19:07:22,193 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 19:07:22,193 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 19:07:22,285 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-15 19:07:22,286 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 19:07:22,289 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:07:22,307 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 19:07:22,335 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:07:22,339 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-15 19:07:22,769 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-15 19:07:22,792 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-15 19:07:22,802 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-15 19:07:22,946 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-15 19:07:22,947 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-15 19:07:25,808 - INFO - معالجة الأمر: /start
2025-07-15 19:07:26,159 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 19:07:28,160 - INFO - معالجة callback: single_card
2025-07-15 19:07:28,399 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-15 19:07:28,401 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-15 19:07:28,664 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-15 19:07:31,489 - INFO - معالجة callback: card_count_1
2025-07-15 19:07:31,759 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-15 19:07:32,140 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-15 19:07:34,158 - INFO - معالجة callback: cards_template_1_10
2025-07-15 19:07:34,382 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 19:07:34,663 - INFO - 🔄 إغلاق نافذة User Manager والانتقال إلى Hotspot...
2025-07-15 19:07:34,663 - INFO - 🚀 تم بدء عملية إنشاء الكرت مع إغلاق User Manager والانتقال لـ Hotspot
2025-07-15 19:07:34,708 - INFO - ✅ تم حفظ إعدادات User Manager قبل الإغلاق
2025-07-15 19:07:36,753 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-15 19:07:36,754 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 19:07:36,755 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:07:36,871 - ERROR - ❌ خطأ في إعادة إنشاء الواجهة: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 19:07:36,872 - INFO - النظام hotspot مفعل بالفعل
2025-07-15 19:07:36,873 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 19:07:36,874 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 19:07:36,874 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 19:07:36,892 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 19:07:36,892 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 19:07:37,216 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 19:07:37,216 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 19:07:37,219 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:07:37,223 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 19:07:37,223 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 19:07:37,224 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 19:07:37,232 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 19:07:37,267 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 19:07:37,270 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:07:37,271 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 19:07:37,272 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 19:07:37,272 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 19:07:37,273 - INFO - 🏭 بدء توليد الكرت...
2025-07-15 19:07:37,305 - INFO - تم توليد 1 حساب
2025-07-15 19:07:37,313 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 203 (من generate_all)
2025-07-15 19:07:37,318 - ERROR - ❌ خطأ في تنفيذ إنشاء كرت واحد: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 19:07:47,594 - INFO - معالجة callback: cards_template_1_10
2025-07-15 19:07:47,823 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 19:07:48,114 - INFO - 🔄 إغلاق نافذة User Manager والانتقال إلى Hotspot...
2025-07-15 19:07:48,114 - INFO - 🚀 تم بدء عملية إنشاء الكرت مع إغلاق User Manager والانتقال لـ Hotspot
2025-07-15 19:07:48,114 - INFO - النظام hotspot مفعل بالفعل
2025-07-15 19:07:48,116 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-15 19:07:48,118 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-15 19:07:48,172 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 19:07:48,199 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 19:07:48,199 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 19:07:48,219 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 19:07:48,226 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 19:07:48,256 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 19:07:48,275 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 19:07:48,278 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:07:48,281 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 19:07:48,282 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 19:07:48,282 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 19:07:48,286 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 19:07:48,296 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 19:07:48,300 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:07:48,300 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 19:07:48,301 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 19:07:48,302 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 19:07:48,302 - INFO - 🏭 بدء توليد الكرت...
2025-07-15 19:07:48,312 - INFO - تم توليد 1 حساب
2025-07-15 19:07:48,334 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 204 (من generate_all)
2025-07-15 19:07:48,343 - ERROR - ❌ خطأ في تنفيذ إنشاء كرت واحد: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 19:07:58,065 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-15 19:07:58,163 - INFO - نجح الاتصال مع *********
2025-07-15 19:07:59,645 - INFO - تم جلب 4 بروفايل يدوياً من النظام: hotspot
2025-07-15 19:08:07,791 - INFO - معالجة الأمر: /start
2025-07-15 19:08:08,053 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 19:08:09,378 - INFO - معالجة callback: single_card
2025-07-15 19:08:09,602 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-15 19:08:09,603 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-15 19:08:09,867 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-15 19:08:11,337 - INFO - معالجة callback: card_count_1
2025-07-15 19:08:11,581 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-15 19:08:11,826 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-15 19:08:13,291 - INFO - معالجة callback: cards_template_1_10
2025-07-15 19:08:13,535 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 19:08:13,808 - INFO - 🔄 إغلاق نافذة User Manager والانتقال إلى Hotspot...
2025-07-15 19:08:13,808 - INFO - 🚀 تم بدء عملية إنشاء الكرت مع إغلاق User Manager والانتقال لـ Hotspot
2025-07-15 19:08:13,808 - INFO - النظام hotspot مفعل بالفعل
2025-07-15 19:08:13,810 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-15 19:08:13,811 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-15 19:08:13,850 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 19:08:13,872 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 19:08:13,873 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 19:08:13,893 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 19:08:13,894 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 19:08:13,924 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 19:08:13,940 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 19:08:13,946 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:08:13,951 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 19:08:13,952 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 19:08:13,953 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 19:08:13,957 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 19:08:13,973 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 19:08:13,996 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:08:13,999 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 19:08:14,000 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 19:08:14,001 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 19:08:14,002 - INFO - 🏭 بدء توليد الكرت...
2025-07-15 19:08:14,009 - INFO - تم توليد 1 حساب
2025-07-15 19:08:14,013 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 205 (من generate_all)
2025-07-15 19:08:14,020 - ERROR - ❌ خطأ في تنفيذ إنشاء كرت واحد: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 19:08:26,449 - INFO - بدء إغلاق التطبيق
2025-07-15 19:08:26,450 - ERROR - خطأ أثناء إغلاق التطبيق: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 19:08:32,551 - INFO - تم بدء تشغيل التطبيق
2025-07-15 19:08:32,552 - INFO - تم إنشاء المجلدات الأساسية
2025-07-15 19:08:32,554 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-15 19:08:32,554 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:08:32,657 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-15 19:08:33,172 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-15 19:08:33,173 - INFO - تم إعداد التطبيق بنجاح
2025-07-15 19:08:35,175 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-15 19:08:35,464 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-15 19:08:35,465 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-15 19:08:38,472 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 19:08:38,473 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 19:08:38,766 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 19:08:38,766 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 19:09:36,451 - INFO - معالجة callback: single_card
2025-07-15 19:09:36,665 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-15 19:09:36,666 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-15 19:09:36,922 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-15 19:09:40,039 - INFO - معالجة callback: card_count_1
2025-07-15 19:09:40,264 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-15 19:09:40,538 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-15 19:09:41,532 - INFO - معالجة callback: cards_template_1_10
2025-07-15 19:09:41,800 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 19:09:42,110 - INFO - 🔄 إغلاق نافذة User Manager والانتقال إلى Hotspot...
2025-07-15 19:09:42,111 - INFO - 🚀 تم بدء عملية إنشاء الكرت مع إغلاق User Manager والانتقال لـ Hotspot
2025-07-15 19:09:42,111 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-15 19:09:42,128 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:09:42,253 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-15 19:09:42,253 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-15 19:09:42,254 - INFO - ✅ تم تبديل النظام من None إلى hotspot
2025-07-15 19:09:42,254 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-15 19:09:42,254 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-15 19:09:42,257 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:09:42,706 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-15 19:09:42,857 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 19:09:42,858 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:09:43,066 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-15 19:09:43,066 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-15 19:09:43,068 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-15 19:09:43,332 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 19:09:43,333 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 19:09:43,333 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 19:09:43,407 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 19:09:43,499 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 19:09:43,588 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 19:09:43,620 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 19:09:43,646 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:09:43,657 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 19:09:43,676 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 19:09:43,677 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 19:09:43,708 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 19:09:43,717 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 19:09:43,744 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:09:43,747 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 19:09:43,750 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 19:09:43,770 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 19:09:43,770 - INFO - 🏭 بدء توليد الكرت...
2025-07-15 19:09:43,776 - INFO - تم توليد 1 حساب
2025-07-15 19:09:43,804 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 203 (من generate_all)
2025-07-15 19:09:43,811 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-15 19:09:43,815 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-15 19:09:43,928 - INFO - نجح الاتصال مع *********
2025-07-15 19:09:44,222 - INFO - ✅ تم إرسال المستخدم: 02907645464
2025-07-15 19:09:44,223 - ERROR - ❌ كرت واحد: خطأ عام في الإرسال: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-15 19:09:44,223 - WARNING - ⚠️ فشل في الإرسال التلقائي، لكن الكرت تم توليده
2025-07-15 19:09:44,488 - INFO - ✅ تم إرسال تفاصيل 1 كرت عبر التلجرام بنجاح
2025-07-15 19:10:19,090 - INFO - معالجة الأمر: /start
2025-07-15 19:10:19,423 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 19:10:20,630 - INFO - معالجة callback: single_card
2025-07-15 19:10:20,879 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-15 19:10:20,880 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-15 19:10:21,139 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-15 19:10:22,100 - INFO - معالجة callback: card_count_1
2025-07-15 19:10:22,326 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-15 19:10:22,578 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-15 19:10:24,323 - INFO - معالجة callback: cards_template_1_10
2025-07-15 19:10:24,569 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 19:10:24,819 - INFO - 🔄 إغلاق نافذة User Manager والانتقال إلى Hotspot...
2025-07-15 19:10:24,820 - INFO - 🚀 تم بدء عملية إنشاء الكرت مع إغلاق User Manager والانتقال لـ Hotspot
2025-07-15 19:10:24,820 - INFO - النظام hotspot مفعل بالفعل
2025-07-15 19:10:24,821 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-15 19:10:24,822 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-15 19:10:24,875 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 19:10:24,876 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 19:10:24,876 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 19:10:24,893 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 19:10:24,894 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 19:10:24,969 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 19:10:24,969 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 19:10:24,973 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:10:24,976 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 19:10:24,976 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 19:10:24,978 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 19:10:24,982 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 19:10:24,994 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 19:10:25,009 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:10:25,017 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 19:10:25,020 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 19:10:25,020 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 19:10:25,020 - INFO - 🏭 بدء توليد الكرت...
2025-07-15 19:10:25,048 - INFO - تم توليد 1 حساب
2025-07-15 19:10:25,049 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 204 (من generate_all)
2025-07-15 19:10:25,056 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-15 19:10:25,061 - INFO - استخدام الاتصال الحالي
2025-07-15 19:10:25,157 - INFO - ✅ تم إرسال المستخدم: 02609105720
2025-07-15 19:10:25,157 - ERROR - ❌ كرت واحد: خطأ عام في الإرسال: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-15 19:10:25,158 - WARNING - ⚠️ فشل في الإرسال التلقائي، لكن الكرت تم توليده
2025-07-15 19:10:25,414 - INFO - ✅ تم إرسال تفاصيل 1 كرت عبر التلجرام بنجاح
2025-07-15 19:11:21,226 - INFO - معالجة الأمر: /start
2025-07-15 19:11:21,483 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 19:11:23,223 - INFO - معالجة callback: select_system_um
2025-07-15 19:11:25,971 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-15 19:11:31,815 - INFO - معالجة الأمر: /restart
2025-07-15 19:11:31,815 - INFO - طلب إعادة تشغيل البرنامج من التلجرام
2025-07-15 19:11:33,123 - INFO - معالجة callback: confirm_restart
2025-07-15 19:11:33,347 - INFO - تم تأكيد إعادة تشغيل البرنامج من التلجرام
2025-07-15 19:11:33,347 - INFO - بدء عملية إعادة تشغيل البرنامج من التلجرام
2025-07-15 19:11:39,196 - INFO - تم تسجيل العملية: إعادة تشغيل من التلجرام - بدء عملية إعادة التشغيل
2025-07-15 19:11:56,915 - INFO - تم بدء تشغيل التطبيق
2025-07-15 19:11:57,156 - INFO - تم إنشاء المجلدات الأساسية
2025-07-15 19:11:57,158 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-15 19:11:57,158 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:11:57,350 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250715_191157.db
2025-07-15 19:11:57,356 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-15 19:11:58,061 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-15 19:11:58,061 - INFO - تم إعداد التطبيق بنجاح
2025-07-15 19:12:00,069 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-15 19:12:00,370 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-15 19:12:00,418 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-15 19:12:03,428 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 19:12:03,430 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 19:12:03,690 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 19:12:03,691 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 19:12:05,915 - INFO - معالجة callback: select_system_um
2025-07-15 19:12:06,430 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-15 19:12:07,582 - INFO - معالجة callback: independent_template_um_10
2025-07-15 19:12:08,236 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-15 19:12:08,245 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-15 19:12:08,550 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-15 19:12:08,573 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:12:08,674 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-15 19:12:08,674 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-15 19:12:08,675 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-15 19:12:08,676 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-15 19:12:08,678 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:12:09,065 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-15 19:12:09,272 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 19:12:09,278 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:12:09,399 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-15 19:12:09,451 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-15 19:12:09,513 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-15 19:12:09,517 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-15 19:12:09,672 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-15 19:12:09,672 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 19:12:09,672 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 19:12:09,709 - INFO - 🖼️ محاولة تحميل صورة الخلفية: img\100100_1.jpg
2025-07-15 19:12:09,774 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-15 19:12:09,794 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-15 19:12:09,794 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 19:12:09,811 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 19:12:09,919 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-15 19:12:09,944 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 19:12:09,947 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:12:09,982 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 19:12:10,003 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:12:10,005 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-15 19:12:10,380 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-15 19:12:10,424 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-15 19:12:10,427 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح
2025-07-15 19:12:10,606 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-15 19:12:10,612 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح
2025-07-15 19:12:14,021 - INFO - معالجة الأمر: /start
2025-07-15 19:12:14,276 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 19:12:15,940 - INFO - معالجة callback: single_card
2025-07-15 19:12:16,176 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-15 19:12:16,177 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-15 19:12:16,483 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-15 19:12:18,115 - INFO - معالجة callback: card_count_1
2025-07-15 19:12:18,379 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-15 19:12:18,627 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-15 19:12:20,385 - INFO - معالجة callback: cards_template_1_10
2025-07-15 19:12:20,607 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 19:12:20,886 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-15 19:12:20,889 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-15 19:12:20,891 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:12:20,994 - INFO - تم حفظ الإعدادات قبل التبديل
2025-07-15 19:12:20,995 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-15 19:12:20,995 - INFO - ✅ تم تبديل النظام من user_manager إلى hotspot
2025-07-15 19:12:20,995 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-15 19:12:20,996 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-15 19:12:20,997 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:12:21,292 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-15 19:12:21,292 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 19:12:21,293 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:12:21,395 - ERROR - خطأ في إعادة بناء الواجهة: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 19:12:21,411 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-15 19:12:21,412 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-15 19:12:21,662 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 19:12:21,665 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 19:12:21,669 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 19:12:21,742 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 19:12:21,744 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 19:12:22,005 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 19:12:22,005 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 19:12:22,008 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:12:22,012 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 19:12:22,012 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 19:12:22,012 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 19:12:22,015 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 19:12:22,024 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 19:12:22,030 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:12:22,032 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 19:12:22,033 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 19:12:22,033 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 19:12:22,033 - INFO - 🏭 بدء توليد الكرت...
2025-07-15 19:12:22,038 - INFO - تم توليد 1 حساب
2025-07-15 19:12:22,039 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 205 (من generate_all)
2025-07-15 19:12:22,044 - ERROR - ❌ خطأ في تنفيذ إنشاء كرت واحد: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 19:12:33,355 - INFO - بدء إغلاق التطبيق
2025-07-15 19:12:33,355 - ERROR - خطأ أثناء إغلاق التطبيق: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 19:12:50,734 - INFO - تم بدء تشغيل التطبيق
2025-07-15 19:12:50,755 - INFO - تم إنشاء المجلدات الأساسية
2025-07-15 19:12:50,758 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-15 19:12:50,759 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:12:50,870 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-15 19:12:51,561 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-15 19:12:51,568 - INFO - تم إعداد التطبيق بنجاح
2025-07-15 19:12:53,568 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-15 19:12:53,871 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-15 19:12:53,871 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-15 19:12:56,878 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 19:12:56,879 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 19:12:57,208 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 19:12:57,208 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 19:12:58,803 - INFO - معالجة callback: select_system_um
2025-07-15 19:12:59,312 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-15 19:13:00,720 - INFO - معالجة callback: independent_template_um_10
2025-07-15 19:13:01,220 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-15 19:13:01,220 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-15 19:13:01,490 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-15 19:13:01,502 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:13:01,603 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-15 19:13:01,603 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-15 19:13:01,604 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-15 19:13:01,605 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-15 19:13:01,608 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:13:01,994 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-15 19:13:02,171 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 19:13:02,172 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:13:02,378 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-15 19:13:02,378 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-15 19:13:02,442 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-15 19:13:02,446 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-15 19:13:02,449 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-15 19:13:02,450 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 19:13:02,450 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 19:13:02,700 - INFO - 🖼️ محاولة تحميل صورة الخلفية: img\100100_1.jpg
2025-07-15 19:13:02,701 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-15 19:13:02,701 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-15 19:13:02,701 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 19:13:02,702 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 19:13:02,793 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-15 19:13:02,794 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 19:13:02,812 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:13:02,822 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 19:13:02,825 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:13:02,827 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-15 19:13:03,110 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-15 19:13:03,298 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-15 19:13:03,299 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-15 19:13:03,433 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-15 19:13:03,434 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-15 19:13:05,096 - INFO - معالجة الأمر: /start
2025-07-15 19:13:05,419 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 19:13:06,783 - INFO - معالجة callback: single_card
2025-07-15 19:13:07,063 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-15 19:13:07,064 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-15 19:13:07,314 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-15 19:13:08,529 - INFO - معالجة callback: card_count_1
2025-07-15 19:13:08,773 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-15 19:13:09,038 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-15 19:13:10,839 - INFO - معالجة callback: cards_template_1_10
2025-07-15 19:13:11,090 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 19:13:11,359 - INFO - 🔄 إغلاق نافذة User Manager والانتقال إلى Hotspot...
2025-07-15 19:13:11,359 - INFO - 🚀 تم بدء عملية إنشاء الكرت مع إغلاق User Manager والانتقال لـ Hotspot
2025-07-15 19:13:11,363 - INFO - ✅ تم حفظ إعدادات User Manager قبل الإغلاق
2025-07-15 19:13:13,061 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-15 19:13:13,061 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 19:13:13,062 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:13:13,176 - ERROR - ❌ خطأ في إعادة إنشاء الواجهة: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 19:13:13,177 - INFO - النظام hotspot مفعل بالفعل
2025-07-15 19:13:13,177 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 19:13:13,177 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 19:13:13,178 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 19:13:13,194 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 19:13:13,195 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 19:13:13,487 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 19:13:13,489 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 19:13:13,506 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:13:13,510 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 19:13:13,510 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 19:13:13,510 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 19:13:13,513 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 19:13:13,522 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 19:13:13,525 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:13:13,529 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 19:13:13,530 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 19:13:13,530 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 19:13:13,530 - INFO - 🏭 بدء توليد الكرت...
2025-07-15 19:13:13,535 - INFO - تم توليد 1 حساب
2025-07-15 19:13:13,536 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 205 (من generate_all)
2025-07-15 19:13:13,541 - ERROR - ❌ خطأ في تنفيذ إنشاء كرت واحد: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 19:13:21,321 - INFO - معالجة الأمر: /start
2025-07-15 19:13:21,617 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 19:13:22,658 - INFO - معالجة callback: select_system_um
2025-07-15 19:13:23,128 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-15 19:13:24,141 - INFO - معالجة callback: independent_template_um_10
2025-07-15 19:13:24,671 - INFO - 🔄 تبديل النظام تلقائياً من hotspot إلى user_manager
2025-07-15 19:13:24,671 - INFO - 🔄 طلب تبديل النظام من التلجرام: hotspot → user_manager
2025-07-15 19:13:24,928 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-15 19:13:24,931 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:13:25,035 - WARNING - تحذير: فشل في حفظ الإعدادات: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 19:13:25,035 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-15 19:13:25,036 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-15 19:13:25,037 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-15 19:13:25,038 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:13:25,294 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-15 19:13:25,318 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 19:13:25,318 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:13:25,502 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-15 19:13:25,502 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-15 19:13:25,521 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-15 19:13:25,530 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من hotspot إلى user_manager
2025-07-15 19:13:25,534 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-15 19:13:25,548 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 19:13:25,550 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 19:13:25,733 - INFO - 🖼️ محاولة تحميل صورة الخلفية: img\100100_1.jpg
2025-07-15 19:13:25,733 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-15 19:13:25,733 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-15 19:13:25,734 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 19:13:25,734 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 19:13:25,824 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-15 19:13:25,824 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 19:13:25,827 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:13:25,846 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 19:13:25,849 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:13:25,850 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-15 19:13:26,134 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-15 19:13:26,331 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-15 19:13:26,332 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-15 19:13:26,452 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-15 19:13:26,453 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-15 19:13:32,674 - INFO - معالجة الأمر: /start
2025-07-15 19:13:32,938 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 19:13:34,102 - INFO - معالجة callback: single_card
2025-07-15 19:13:34,380 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-15 19:13:34,381 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-15 19:13:34,727 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-15 19:13:35,661 - INFO - معالجة callback: card_count_1
2025-07-15 19:13:35,873 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-15 19:13:36,138 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-15 19:13:37,056 - INFO - معالجة callback: cards_template_1_10
2025-07-15 19:13:37,280 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 19:13:37,558 - INFO - 🔄 إغلاق نافذة User Manager والانتقال إلى Hotspot...
2025-07-15 19:13:37,558 - INFO - 🚀 تم بدء عملية إنشاء الكرت مع إغلاق User Manager والانتقال لـ Hotspot
2025-07-15 19:13:37,562 - INFO - ✅ تم حفظ إعدادات User Manager قبل الإغلاق
2025-07-15 19:13:39,374 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-15 19:13:39,375 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 19:13:39,375 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:13:39,491 - ERROR - ❌ خطأ في إعادة إنشاء الواجهة: invalid command name ".!canvas3.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 19:13:39,491 - INFO - النظام hotspot مفعل بالفعل
2025-07-15 19:13:39,491 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 19:13:39,494 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 19:13:39,495 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 19:13:39,512 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 19:13:39,513 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 19:13:39,871 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 19:13:39,871 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 19:13:39,874 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:13:39,877 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 19:13:39,877 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 19:13:39,878 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 19:13:39,880 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 19:13:39,889 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 19:13:39,911 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:13:39,911 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 19:13:39,912 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 19:13:39,913 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 19:13:39,913 - INFO - 🏭 بدء توليد الكرت...
2025-07-15 19:13:39,919 - INFO - تم توليد 1 حساب
2025-07-15 19:13:39,919 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 205 (من generate_all)
2025-07-15 19:13:39,924 - ERROR - ❌ خطأ في تنفيذ إنشاء كرت واحد: invalid command name ".!canvas3.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 19:13:49,246 - INFO - بدء إغلاق التطبيق
2025-07-15 19:13:49,246 - ERROR - خطأ أثناء إغلاق التطبيق: invalid command name ".!canvas3.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 19:14:13,511 - INFO - تم بدء تشغيل التطبيق
2025-07-15 19:14:13,590 - INFO - تم إنشاء المجلدات الأساسية
2025-07-15 19:14:13,596 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-15 19:14:13,603 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:14:13,705 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-15 19:14:14,079 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-15 19:14:14,080 - INFO - تم إعداد التطبيق بنجاح
2025-07-15 19:14:16,091 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-15 19:14:16,424 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-15 19:14:16,425 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-15 19:14:19,426 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 19:14:19,427 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 19:14:19,736 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 19:14:19,737 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 19:14:31,724 - INFO - معالجة callback: select_system_um
2025-07-15 19:14:32,183 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-15 19:14:33,486 - INFO - معالجة callback: independent_template_um_10
2025-07-15 19:14:33,998 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-15 19:14:33,998 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-15 19:14:34,271 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-15 19:14:34,310 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:14:34,411 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-15 19:14:34,417 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-15 19:14:34,417 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-15 19:14:34,418 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-15 19:14:34,424 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:14:34,808 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-15 19:14:34,977 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 19:14:34,977 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:14:35,188 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-15 19:14:35,188 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-15 19:14:35,259 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-15 19:14:35,264 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-15 19:14:35,266 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-15 19:14:35,266 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 19:14:35,267 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 19:14:35,496 - INFO - 🖼️ محاولة تحميل صورة الخلفية: img\100100_1.jpg
2025-07-15 19:14:35,498 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-15 19:14:35,499 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-15 19:14:35,499 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 19:14:35,500 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 19:14:35,594 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-15 19:14:35,595 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 19:14:35,598 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:14:35,616 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 19:14:35,620 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:14:35,643 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-15 19:14:36,217 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-15 19:14:36,291 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-15 19:14:36,304 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-15 19:14:36,331 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-15 19:14:36,503 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-15 19:14:41,507 - INFO - معالجة الأمر: /start
2025-07-15 19:14:41,845 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 19:14:43,401 - INFO - معالجة callback: single_card
2025-07-15 19:14:43,701 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-15 19:14:43,702 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-15 19:14:43,968 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-15 19:14:44,991 - INFO - معالجة callback: card_count_1
2025-07-15 19:14:45,246 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-15 19:14:45,511 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-15 19:14:46,389 - INFO - معالجة callback: cards_template_1_10
2025-07-15 19:14:46,645 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 19:14:46,961 - INFO - 🔄 إغلاق نافذة User Manager والانتقال إلى Hotspot...
2025-07-15 19:14:46,961 - INFO - 🚀 تم بدء عملية إنشاء الكرت مع إغلاق User Manager والانتقال لـ Hotspot
2025-07-15 19:14:46,966 - INFO - ✅ تم حفظ إعدادات User Manager قبل الإغلاق
2025-07-15 19:14:48,675 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-15 19:14:48,676 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 19:14:48,676 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:14:48,796 - ERROR - ❌ خطأ في إعادة إنشاء الواجهة: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 19:14:48,797 - INFO - النظام hotspot مفعل بالفعل
2025-07-15 19:14:48,797 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 19:14:48,798 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 19:14:48,798 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 19:14:48,818 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 19:14:48,819 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 19:14:49,192 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 19:14:49,193 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 19:14:49,196 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:14:49,199 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 19:14:49,199 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 19:14:49,200 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 19:14:49,206 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 19:14:49,241 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 19:14:49,248 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:14:49,268 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 19:14:49,269 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 19:14:49,270 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 19:14:49,270 - INFO - 🏭 بدء توليد الكرت...
2025-07-15 19:14:49,275 - INFO - تم توليد 1 حساب
2025-07-15 19:14:49,302 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 205 (من generate_all)
2025-07-15 19:14:49,307 - ERROR - ❌ خطأ في تنفيذ إنشاء كرت واحد: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 19:35:02,700 - INFO - تم بدء تشغيل التطبيق
2025-07-15 19:35:02,800 - INFO - تم إنشاء المجلدات الأساسية
2025-07-15 19:35:02,822 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-15 19:35:02,857 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:35:02,981 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-15 19:35:03,456 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-15 19:35:03,457 - INFO - تم إعداد التطبيق بنجاح
2025-07-15 19:35:05,489 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-15 19:35:06,102 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-15 19:35:06,110 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-15 19:35:09,110 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 19:35:09,151 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 19:35:09,479 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 19:35:09,480 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 19:36:00,834 - INFO - معالجة callback: select_system_um
2025-07-15 19:36:01,323 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-15 19:36:02,386 - INFO - معالجة callback: independent_template_um_10
2025-07-15 19:36:02,886 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-15 19:36:02,887 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-15 19:36:03,170 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-15 19:36:03,296 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:36:03,396 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-15 19:36:03,397 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-15 19:36:03,399 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-15 19:36:03,401 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-15 19:36:03,405 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:36:03,947 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-15 19:36:04,136 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 19:36:04,137 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:36:04,854 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-15 19:36:04,855 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-15 19:36:04,922 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-15 19:36:04,925 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-15 19:36:04,946 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-15 19:36:04,948 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 19:36:04,957 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 19:36:05,357 - INFO - 🖼️ محاولة تحميل صورة الخلفية: img\100100_1.jpg
2025-07-15 19:36:05,358 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-15 19:36:05,358 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-15 19:36:05,358 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 19:36:05,359 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 19:36:05,453 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-15 19:36:05,454 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 19:36:05,457 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:36:05,474 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 19:36:05,492 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:36:05,496 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-15 19:36:05,756 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-15 19:36:05,955 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-15 19:36:05,956 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-15 19:36:06,093 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-15 19:36:06,094 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-15 19:36:08,173 - INFO - معالجة الأمر: /start
2025-07-15 19:36:08,426 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 19:36:09,903 - INFO - معالجة callback: single_card
2025-07-15 19:36:10,177 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-15 19:36:10,177 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-15 19:36:10,453 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-15 19:36:11,633 - INFO - معالجة callback: card_count_1
2025-07-15 19:36:11,859 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-15 19:36:12,149 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-15 19:36:13,646 - INFO - معالجة callback: cards_template_1_10
2025-07-15 19:36:13,980 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 19:36:14,234 - INFO - 🔄 إغلاق نافذة User Manager والانتقال إلى Hotspot...
2025-07-15 19:36:14,234 - INFO - 🚀 تم بدء عملية إنشاء الكرت مع إغلاق User Manager والانتقال لـ Hotspot
2025-07-15 19:36:14,237 - INFO - ✅ تم حفظ إعدادات User Manager قبل الإغلاق
2025-07-15 19:36:16,027 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-15 19:36:16,028 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 19:36:16,029 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:36:16,144 - ERROR - ❌ خطأ في إعادة إنشاء الواجهة: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 19:36:16,144 - INFO - النظام hotspot مفعل بالفعل
2025-07-15 19:36:16,145 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 19:36:16,146 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 19:36:16,146 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 19:36:16,165 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 19:36:16,166 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 19:36:16,459 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 19:36:16,460 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 19:36:16,464 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:36:16,467 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 19:36:16,468 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 19:36:16,470 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 19:36:16,475 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 19:36:16,507 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 19:36:16,510 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:36:16,510 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 19:36:16,512 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 19:36:16,513 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 19:36:16,513 - INFO - 🏭 بدء توليد الكرت...
2025-07-15 19:36:16,532 - INFO - تم توليد 1 حساب
2025-07-15 19:36:16,562 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 205 (من generate_all)
2025-07-15 19:36:16,582 - ERROR - ❌ خطأ في تنفيذ إنشاء كرت واحد: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 19:46:44,490 - INFO - تم بدء تشغيل التطبيق
2025-07-15 19:46:44,546 - INFO - تم إنشاء المجلدات الأساسية
2025-07-15 19:46:44,548 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-15 19:46:44,548 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:46:44,738 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-15 19:46:45,672 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-15 19:46:45,672 - INFO - تم إعداد التطبيق بنجاح
2025-07-15 19:46:47,682 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-15 19:46:47,986 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-15 19:46:47,987 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-15 19:46:51,000 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 19:46:51,065 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 19:46:51,356 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 19:46:51,459 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 19:46:55,519 - INFO - معالجة callback: select_system_um
2025-07-15 19:46:56,168 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-15 19:46:57,436 - INFO - معالجة callback: independent_template_um_10
2025-07-15 19:46:58,078 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-15 19:46:58,079 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-15 19:46:58,378 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-15 19:46:58,481 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:46:58,582 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-15 19:46:58,583 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-15 19:46:58,583 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-15 19:46:58,600 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-15 19:46:58,609 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:46:59,061 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-15 19:46:59,250 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 19:46:59,251 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:46:59,753 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-15 19:46:59,848 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-15 19:46:59,927 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-15 19:46:59,939 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-15 19:47:00,049 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-15 19:47:00,050 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 19:47:00,051 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 19:47:00,292 - INFO - 🖼️ محاولة تحميل صورة الخلفية: img\100100_1.jpg
2025-07-15 19:47:00,477 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-15 19:47:00,538 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-15 19:47:00,539 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 19:47:00,539 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 19:47:00,636 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-15 19:47:00,643 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 19:47:00,658 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:47:00,671 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 19:47:00,673 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:47:00,675 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-15 19:47:00,993 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-15 19:47:01,150 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-15 19:47:01,152 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-15 19:47:01,282 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-15 19:47:01,283 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-15 19:47:05,064 - INFO - معالجة الأمر: /start
2025-07-15 19:47:05,321 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 19:47:06,916 - INFO - معالجة callback: single_card
2025-07-15 19:47:07,328 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-15 19:47:07,329 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-15 19:47:07,651 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-15 19:47:08,939 - INFO - معالجة callback: card_count_1
2025-07-15 19:47:09,151 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-15 19:47:09,401 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-15 19:47:10,433 - INFO - معالجة callback: cards_template_1_10
2025-07-15 19:47:10,724 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 19:47:11,057 - INFO - 🔄 إغلاق نافذة User Manager والانتقال إلى Hotspot...
2025-07-15 19:47:11,057 - INFO - 🚀 تم بدء عملية إنشاء الكرت مع إغلاق User Manager والانتقال لـ Hotspot
2025-07-15 19:47:11,198 - INFO - ✅ تم حفظ إعدادات User Manager قبل الإغلاق
2025-07-15 19:47:12,576 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-15 19:47:12,577 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 19:47:12,577 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:47:12,780 - ERROR - ❌ خطأ في إعادة إنشاء الواجهة: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 19:47:12,780 - INFO - النظام hotspot مفعل بالفعل
2025-07-15 19:47:12,780 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 19:47:12,781 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 19:47:12,781 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 19:47:12,784 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 19:47:12,785 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 19:47:13,231 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 19:47:13,231 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 19:47:13,234 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:47:13,238 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 19:47:13,238 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 19:47:13,240 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 19:47:13,242 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 19:47:13,251 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 19:47:13,254 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:47:13,254 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 19:47:13,257 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 19:47:13,257 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 19:47:13,258 - INFO - 🏭 بدء توليد الكرت...
2025-07-15 19:47:13,264 - INFO - تم توليد 1 حساب
2025-07-15 19:47:13,264 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 205 (من generate_all)
2025-07-15 19:47:13,268 - ERROR - ❌ خطأ في استدعاء generate_all: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 19:47:18,261 - INFO - معالجة callback: cards_template_1_10
2025-07-15 19:47:18,472 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 19:47:18,735 - INFO - 🔄 إغلاق نافذة User Manager والانتقال إلى Hotspot...
2025-07-15 19:47:18,735 - INFO - 🚀 تم بدء عملية إنشاء الكرت مع إغلاق User Manager والانتقال لـ Hotspot
2025-07-15 19:47:18,735 - INFO - النظام hotspot مفعل بالفعل
2025-07-15 19:47:18,736 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-15 19:47:18,737 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-15 19:47:18,790 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 19:47:18,791 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 19:47:18,791 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 19:47:18,812 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 19:47:18,812 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 19:47:18,834 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 19:47:18,834 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 19:47:18,837 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:47:18,842 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 19:47:18,842 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 19:47:18,844 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 19:47:18,846 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 19:47:18,859 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 19:47:18,862 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:47:18,862 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 19:47:18,864 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 19:47:18,864 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 19:47:18,864 - INFO - 🏭 بدء توليد الكرت...
2025-07-15 19:47:18,869 - INFO - تم توليد 1 حساب
2025-07-15 19:47:18,870 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 206 (من generate_all)
2025-07-15 19:47:18,877 - ERROR - ❌ خطأ في استدعاء generate_all: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 19:47:33,534 - INFO - معالجة callback: cards_template_1_10
2025-07-15 19:47:33,743 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 19:47:34,013 - INFO - 🔄 إغلاق نافذة User Manager والانتقال إلى Hotspot...
2025-07-15 19:47:34,013 - INFO - 🚀 تم بدء عملية إنشاء الكرت مع إغلاق User Manager والانتقال لـ Hotspot
2025-07-15 19:47:34,014 - INFO - النظام hotspot مفعل بالفعل
2025-07-15 19:47:34,015 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-15 19:47:34,017 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-15 19:47:34,132 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 19:47:34,133 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 19:47:34,134 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 19:47:34,183 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 19:47:34,183 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 19:47:34,205 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 19:47:34,205 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 19:47:34,208 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:47:34,233 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 19:47:34,233 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 19:47:34,234 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 19:47:34,237 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 19:47:34,265 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 19:47:34,270 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:47:34,271 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 19:47:34,272 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 19:47:34,272 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 19:47:34,272 - INFO - 🏭 بدء توليد الكرت...
2025-07-15 19:47:34,301 - INFO - تم توليد 1 حساب
2025-07-15 19:47:34,302 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 207 (من generate_all)
2025-07-15 19:47:34,329 - ERROR - ❌ خطأ في استدعاء generate_all: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 19:47:52,036 - INFO - معالجة callback: cards_template_1_10
2025-07-15 19:47:52,319 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 19:47:52,657 - INFO - 🔄 إغلاق نافذة User Manager والانتقال إلى Hotspot...
2025-07-15 19:47:52,657 - INFO - 🚀 تم بدء عملية إنشاء الكرت مع إغلاق User Manager والانتقال لـ Hotspot
2025-07-15 19:47:52,660 - INFO - النظام hotspot مفعل بالفعل
2025-07-15 19:47:52,690 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-15 19:47:52,704 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-15 19:47:52,810 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 19:47:52,895 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 19:47:52,913 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 19:47:53,221 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 19:47:53,465 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 19:47:53,528 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 19:47:53,540 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 19:47:53,574 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:47:53,608 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 19:47:53,631 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 19:47:53,637 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 19:47:53,671 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 19:47:53,700 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 19:47:53,741 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:47:53,761 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 19:47:54,413 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 19:47:54,434 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 19:47:54,454 - INFO - 🏭 بدء توليد الكرت...
2025-07-15 19:47:54,617 - INFO - تم توليد 1 حساب
2025-07-15 19:47:54,648 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 208 (من generate_all)
2025-07-15 19:47:54,656 - ERROR - ❌ خطأ في استدعاء generate_all: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 19:49:01,945 - INFO - معالجة الأمر: /start
2025-07-15 19:49:02,231 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 19:49:03,736 - INFO - معالجة callback: single_card
2025-07-15 19:49:03,948 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-15 19:49:03,949 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-15 19:49:04,198 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-15 19:49:05,316 - INFO - معالجة callback: card_count_1
2025-07-15 19:49:05,528 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-15 19:49:05,798 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-15 19:49:06,772 - INFO - معالجة callback: cards_template_1_10
2025-07-15 19:49:06,986 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 19:49:07,249 - INFO - 🔄 إغلاق نافذة User Manager والانتقال إلى Hotspot...
2025-07-15 19:49:07,249 - INFO - 🚀 تم بدء عملية إنشاء الكرت مع إغلاق User Manager والانتقال لـ Hotspot
2025-07-15 19:49:07,249 - INFO - النظام hotspot مفعل بالفعل
2025-07-15 19:49:07,250 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-15 19:49:07,252 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-15 19:49:07,328 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 19:49:07,329 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 19:49:07,329 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 19:49:07,424 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 19:49:07,424 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 19:49:07,453 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 19:49:07,453 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 19:49:07,457 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:49:07,490 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 19:49:07,490 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 19:49:07,491 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 19:49:07,535 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 19:49:07,601 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 19:49:07,607 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:49:07,608 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 19:49:07,610 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 19:49:07,610 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 19:49:07,610 - INFO - 🏭 بدء توليد الكرت...
2025-07-15 19:49:07,643 - INFO - تم توليد 1 حساب
2025-07-15 19:49:07,644 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 209 (من generate_all)
2025-07-15 19:49:07,649 - ERROR - ❌ خطأ في استدعاء generate_all: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 19:49:21,130 - INFO - معالجة الأمر: /restart
2025-07-15 19:49:21,131 - INFO - طلب إعادة تشغيل البرنامج من التلجرام
2025-07-15 19:49:22,271 - INFO - معالجة callback: confirm_restart
2025-07-15 19:49:22,488 - INFO - تم تأكيد إعادة تشغيل البرنامج من التلجرام
2025-07-15 19:49:22,489 - INFO - بدء عملية إعادة تشغيل البرنامج من التلجرام
2025-07-15 19:49:28,397 - INFO - تم تسجيل العملية: إعادة تشغيل من التلجرام - بدء عملية إعادة التشغيل
2025-07-15 19:49:41,385 - INFO - تم بدء تشغيل التطبيق
2025-07-15 19:49:41,483 - INFO - تم إنشاء المجلدات الأساسية
2025-07-15 19:49:41,488 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-15 19:49:41,489 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:49:41,619 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250715_194941.db
2025-07-15 19:49:41,798 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-15 19:49:42,228 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-15 19:49:42,232 - INFO - تم إعداد التطبيق بنجاح
2025-07-15 19:49:44,246 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-15 19:49:44,663 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-15 19:49:44,795 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-15 19:49:47,820 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 19:49:47,824 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 19:49:48,077 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 19:49:48,079 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 19:50:08,432 - INFO - معالجة callback: single_card
2025-07-15 19:50:08,675 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-15 19:50:08,676 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-15 19:50:08,966 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-15 19:50:09,873 - INFO - معالجة callback: card_count_1
2025-07-15 19:50:10,124 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-15 19:50:10,394 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-15 19:50:13,027 - INFO - معالجة callback: cards_template_1_10
2025-07-15 19:50:13,281 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 19:50:13,553 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-15 19:50:13,556 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-15 19:50:13,601 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:50:13,702 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-15 19:50:13,702 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-15 19:50:13,702 - INFO - ✅ تم تبديل النظام من None إلى hotspot
2025-07-15 19:50:13,702 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-15 19:50:13,703 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-15 19:50:13,704 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:50:14,142 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-15 19:50:14,281 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 19:50:14,281 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:50:14,401 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-15 19:50:14,402 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-15 19:50:14,403 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-15 19:50:14,690 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 19:50:14,692 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 19:50:14,696 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 19:50:14,744 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 19:50:14,762 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 19:50:14,786 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 19:50:14,788 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 19:50:14,791 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:50:14,795 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 19:50:14,795 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 19:50:14,795 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 19:50:14,798 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 19:50:14,807 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 19:50:14,814 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:50:14,814 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 19:50:14,816 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 19:50:14,816 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 19:50:14,817 - INFO - 🏭 بدء توليد الكرت...
2025-07-15 19:50:14,821 - INFO - تم توليد 1 حساب
2025-07-15 19:50:14,823 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 205 (من generate_all)
2025-07-15 19:50:14,830 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-15 19:50:14,830 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-15 19:50:14,909 - INFO - نجح الاتصال مع *********
2025-07-15 19:50:15,009 - INFO - ✅ تم إرسال المستخدم: 02950258707
2025-07-15 19:50:15,009 - ERROR - ❌ كرت واحد: خطأ عام في الإرسال: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-15 19:50:15,009 - WARNING - ⚠️ فشل في الإرسال التلقائي، لكن الكرت تم توليده
2025-07-15 19:50:15,400 - INFO - ✅ تم إرسال تفاصيل 1 كرت عبر التلجرام بنجاح
2025-07-15 19:50:25,856 - INFO - معالجة callback: cards_template_1_10
2025-07-15 19:50:26,143 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 19:50:26,404 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-15 19:50:26,407 - INFO - النظام hotspot مفعل بالفعل
2025-07-15 19:50:26,407 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-15 19:50:26,407 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-15 19:50:26,446 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 19:50:26,448 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 19:50:26,449 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 19:50:26,468 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 19:50:26,469 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 19:50:26,490 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 19:50:26,490 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 19:50:26,494 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:50:26,501 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 19:50:26,501 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 19:50:26,502 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 19:50:26,504 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 19:50:26,513 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 19:50:26,518 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:50:26,518 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 19:50:26,520 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 19:50:26,520 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 19:50:26,522 - INFO - 🏭 بدء توليد الكرت...
2025-07-15 19:50:26,531 - INFO - تم توليد 1 حساب
2025-07-15 19:50:26,535 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 206 (من generate_all)
2025-07-15 19:50:26,541 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-15 19:50:26,544 - INFO - استخدام الاتصال الحالي
2025-07-15 19:50:26,623 - INFO - ✅ تم إرسال المستخدم: 02893158992
2025-07-15 19:50:26,624 - ERROR - ❌ كرت واحد: خطأ عام في الإرسال: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-15 19:50:26,624 - WARNING - ⚠️ فشل في الإرسال التلقائي، لكن الكرت تم توليده
2025-07-15 19:50:26,886 - INFO - ✅ تم إرسال تفاصيل 1 كرت عبر التلجرام بنجاح
2025-07-15 19:50:36,045 - INFO - معالجة callback: cards_template_1_10
2025-07-15 19:50:36,256 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 19:50:36,525 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-15 19:50:36,526 - INFO - النظام hotspot مفعل بالفعل
2025-07-15 19:50:36,527 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-15 19:50:36,527 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-15 19:50:36,564 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 19:50:36,566 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 19:50:36,566 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 19:50:36,587 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 19:50:36,591 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 19:50:36,612 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 19:50:36,612 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 19:50:36,616 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:50:36,619 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 19:50:36,622 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 19:50:36,623 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 19:50:36,629 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 19:50:36,638 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 19:50:36,641 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:50:36,641 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 19:50:36,642 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 19:50:36,642 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 19:50:36,643 - INFO - 🏭 بدء توليد الكرت...
2025-07-15 19:50:36,648 - INFO - تم توليد 1 حساب
2025-07-15 19:50:36,658 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 207 (من generate_all)
2025-07-15 19:50:36,663 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-15 19:50:36,666 - INFO - استخدام الاتصال الحالي
2025-07-15 19:50:36,753 - INFO - ✅ تم إرسال المستخدم: 02264411686
2025-07-15 19:50:36,753 - ERROR - ❌ كرت واحد: خطأ عام في الإرسال: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-15 19:50:36,754 - WARNING - ⚠️ فشل في الإرسال التلقائي، لكن الكرت تم توليده
2025-07-15 19:50:37,016 - INFO - ✅ تم إرسال تفاصيل 1 كرت عبر التلجرام بنجاح
2025-07-15 19:50:45,866 - INFO - بدء إغلاق التطبيق
2025-07-15 19:50:45,868 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250715_195045.db
2025-07-15 19:50:45,870 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250715_195045.db
2025-07-15 19:50:45,871 - INFO - تم قطع الاتصال مع MikroTik
2025-07-15 19:50:45,875 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-15 19:50:45,876 - INFO - تم إغلاق التطبيق بنجاح
2025-07-15 19:56:06,426 - INFO - تم بدء تشغيل التطبيق
2025-07-15 19:56:06,530 - INFO - تم إنشاء المجلدات الأساسية
2025-07-15 19:56:06,532 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-15 19:56:06,532 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:56:06,634 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-15 19:56:07,096 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-15 19:56:07,097 - INFO - تم إعداد التطبيق بنجاح
2025-07-15 19:56:09,103 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-15 19:56:09,417 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-15 19:56:09,440 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-15 19:56:12,447 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 19:56:12,449 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 19:56:12,735 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 19:56:12,799 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 19:56:13,846 - INFO - معالجة callback: select_system_um
2025-07-15 19:56:14,315 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-15 19:56:15,289 - INFO - معالجة callback: independent_template_um_10
2025-07-15 19:56:15,800 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-15 19:56:15,801 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-15 19:56:16,063 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-15 19:56:16,334 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:56:16,435 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-15 19:56:16,435 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-15 19:56:16,436 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-15 19:56:16,436 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-15 19:56:16,438 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:56:16,871 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-15 19:56:17,047 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 19:56:17,047 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:56:17,175 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-15 19:56:17,175 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-15 19:56:17,387 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-15 19:56:17,389 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-15 19:56:17,391 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-15 19:56:17,392 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 19:56:17,392 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 19:56:17,395 - INFO - 🖼️ محاولة تحميل صورة الخلفية: img\100100_1.jpg
2025-07-15 19:56:17,736 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-15 19:56:17,801 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-15 19:56:17,801 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 19:56:17,802 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 19:56:17,930 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-15 19:56:17,930 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 19:56:17,935 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:56:17,946 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 19:56:17,948 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:56:17,950 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-15 19:56:18,225 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-15 19:56:18,432 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-15 19:56:18,433 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-15 19:56:18,549 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-15 19:56:18,550 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-15 19:56:19,835 - INFO - معالجة الأمر: /start
2025-07-15 19:56:20,086 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 19:56:21,409 - INFO - معالجة callback: single_card
2025-07-15 19:56:21,667 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-15 19:56:21,668 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-15 19:56:21,995 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-15 19:56:23,705 - INFO - معالجة callback: card_count_1
2025-07-15 19:56:23,934 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-15 19:56:24,178 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-15 19:56:25,240 - INFO - معالجة callback: cards_template_1_10
2025-07-15 19:56:25,464 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 19:56:25,725 - INFO - 🔄 إغلاق نافذة User Manager والانتقال إلى Hotspot...
2025-07-15 19:56:25,725 - INFO - 🚀 تم بدء عملية إنشاء الكرت مع إغلاق User Manager والانتقال لـ Hotspot
2025-07-15 19:56:25,729 - INFO - ✅ تم حفظ إعدادات User Manager قبل الإغلاق
2025-07-15 19:56:28,257 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-15 19:56:28,257 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 19:56:28,258 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 19:56:28,408 - ERROR - ❌ خطأ في إعادة إنشاء الواجهة: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 19:56:28,408 - INFO - النظام hotspot مفعل بالفعل
2025-07-15 19:56:28,408 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 19:56:28,409 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 19:56:28,409 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 19:56:28,437 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 19:56:28,437 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 19:56:28,891 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 19:56:28,891 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 19:56:28,894 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:56:28,897 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 19:56:28,897 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 19:56:29,200 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 19:56:29,203 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 19:56:29,212 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 19:56:29,216 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:56:29,216 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 19:56:29,217 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 19:56:29,218 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 19:56:29,218 - INFO - ⏳ التحقق من جاهزية الواجهة قبل بدء التوليد...
2025-07-15 19:56:29,218 - INFO - ⏳ انتظار جاهزية الواجهة (حد أقصى 3 ثانية)...
2025-07-15 19:56:29,218 - INFO - ✅ الواجهة جاهزة بعد 0.00 ثانية
2025-07-15 19:56:29,219 - INFO - ✅ جميع متطلبات generate_all متوفرة
2025-07-15 19:56:29,219 - INFO - 🏭 بدء توليد الكرت...
2025-07-15 19:56:29,425 - INFO - تم توليد 1 حساب
2025-07-15 19:56:29,425 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 208 (من generate_all)
2025-07-15 19:56:29,429 - ERROR - ❌ خطأ في استدعاء generate_all: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 19:56:29,430 - ERROR - 🔍 تشخيص: الخطأ مرتبط بعنصر GUI غير صالح
2025-07-15 19:56:29,430 - INFO - 🔄 محاولة إضافية مع تأخير أطول...
2025-07-15 19:56:29,931 - INFO - ✅ جميع متطلبات generate_all متوفرة
2025-07-15 19:56:29,942 - INFO - تم توليد 1 حساب
2025-07-15 19:56:29,942 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 209 (من generate_all)
2025-07-15 19:56:29,947 - ERROR - ❌ فشلت المحاولة الإضافية: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 19:56:43,917 - INFO - معالجة callback: cards_template_1_10
2025-07-15 19:56:44,146 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 19:56:44,417 - INFO - 🔄 إغلاق نافذة User Manager والانتقال إلى Hotspot...
2025-07-15 19:56:44,417 - INFO - 🚀 تم بدء عملية إنشاء الكرت مع إغلاق User Manager والانتقال لـ Hotspot
2025-07-15 19:56:44,417 - INFO - النظام hotspot مفعل بالفعل
2025-07-15 19:56:44,418 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-15 19:56:44,419 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-15 19:56:44,511 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 19:56:44,512 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 19:56:44,513 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 19:56:44,571 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 19:56:44,571 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 19:56:44,645 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 19:56:44,646 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 19:56:44,660 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:56:44,663 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 19:56:44,663 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 19:56:44,967 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 19:56:44,970 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 19:56:45,011 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 19:56:45,015 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 19:56:45,015 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 19:56:45,017 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 19:56:45,018 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 19:56:45,018 - INFO - ⏳ التحقق من جاهزية الواجهة قبل بدء التوليد...
2025-07-15 19:56:45,018 - INFO - ⏳ انتظار جاهزية الواجهة (حد أقصى 3 ثانية)...
2025-07-15 19:56:45,019 - INFO - ✅ الواجهة جاهزة بعد 0.00 ثانية
2025-07-15 19:56:45,019 - INFO - ✅ جميع متطلبات generate_all متوفرة
2025-07-15 19:56:45,019 - INFO - 🏭 بدء توليد الكرت...
2025-07-15 19:56:45,231 - INFO - تم توليد 1 حساب
2025-07-15 19:56:45,232 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 210 (من generate_all)
2025-07-15 19:56:45,237 - ERROR - ❌ خطأ في استدعاء generate_all: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 19:56:45,237 - ERROR - 🔍 تشخيص: الخطأ مرتبط بعنصر GUI غير صالح
2025-07-15 19:56:45,238 - INFO - 🔄 محاولة إضافية مع تأخير أطول...
2025-07-15 19:56:45,739 - INFO - ✅ جميع متطلبات generate_all متوفرة
2025-07-15 19:56:45,748 - INFO - تم توليد 1 حساب
2025-07-15 19:56:45,749 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 211 (من generate_all)
2025-07-15 19:56:45,803 - ERROR - ❌ فشلت المحاولة الإضافية: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 20:09:46,189 - INFO - بدء إغلاق التطبيق
2025-07-15 20:09:46,189 - ERROR - خطأ أثناء إغلاق التطبيق: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 20:24:53,309 - INFO - تم بدء تشغيل التطبيق
2025-07-15 20:24:53,328 - INFO - تم إنشاء المجلدات الأساسية
2025-07-15 20:24:53,332 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-15 20:24:53,332 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-15 20:24:53,435 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-15 20:24:54,134 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-15 20:24:54,141 - INFO - تم إعداد التطبيق بنجاح
2025-07-15 20:24:56,143 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-15 20:24:56,646 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-15 20:24:56,647 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-15 20:24:59,651 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 20:24:59,792 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 20:25:00,148 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 20:25:00,149 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 20:25:01,699 - INFO - معالجة callback: select_system_um
2025-07-15 20:25:02,271 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-15 20:25:03,284 - INFO - معالجة callback: independent_template_um_10
2025-07-15 20:25:03,985 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-15 20:25:03,986 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-15 20:25:04,285 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-15 20:25:04,331 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-15 20:25:04,432 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-15 20:25:04,433 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-15 20:25:04,433 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-15 20:25:04,434 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-15 20:25:04,437 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-15 20:25:04,826 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-15 20:25:04,993 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 20:25:04,994 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 20:25:05,482 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-15 20:25:05,483 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-15 20:25:05,549 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-15 20:25:05,561 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-15 20:25:05,565 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-15 20:25:05,566 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 20:25:05,566 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 20:25:05,793 - INFO - 🖼️ محاولة تحميل صورة الخلفية: img\100100_1.jpg
2025-07-15 20:25:05,794 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-15 20:25:05,794 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-15 20:25:05,795 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 20:25:05,795 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 20:25:05,893 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-15 20:25:05,893 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 20:25:05,907 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 20:25:05,920 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 20:25:05,945 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 20:25:05,946 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-15 20:25:06,250 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-15 20:25:06,393 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-15 20:25:06,394 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-15 20:25:06,544 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-15 20:25:06,545 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-15 20:25:07,637 - INFO - معالجة الأمر: /start
2025-07-15 20:25:07,904 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 20:25:09,454 - INFO - معالجة callback: single_card
2025-07-15 20:25:09,682 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-15 20:25:09,683 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-15 20:25:09,948 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-15 20:25:11,094 - INFO - معالجة callback: card_count_1
2025-07-15 20:25:11,311 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-15 20:25:11,581 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-15 20:25:13,621 - INFO - معالجة callback: cards_template_1_10
2025-07-15 20:25:13,881 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 20:25:14,205 - INFO - 🔄 إغلاق نافذة User Manager والانتقال إلى Hotspot...
2025-07-15 20:25:14,205 - INFO - 🚀 تم بدء عملية إنشاء الكرت مع إغلاق User Manager والانتقال لـ Hotspot
2025-07-15 20:25:14,210 - INFO - ✅ تم حفظ إعدادات User Manager قبل الإغلاق
2025-07-15 20:25:15,800 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-15 20:25:15,801 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 20:25:15,801 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 20:25:15,916 - ERROR - ❌ خطأ في إعادة إنشاء الواجهة: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 20:25:15,916 - INFO - النظام hotspot مفعل بالفعل
2025-07-15 20:25:15,917 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 20:25:15,917 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 20:25:15,918 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 20:25:15,935 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 20:25:15,937 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 20:25:16,296 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 20:25:16,296 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 20:25:16,299 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 20:25:16,303 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 20:25:16,303 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 20:25:16,630 - INFO - 🔄 فرض مزامنة إضافية بعد تطبيق القالب...
2025-07-15 20:25:16,630 - INFO - 🔄 بدء فرض المزامنة الكاملة للواجهة...
2025-07-15 20:25:16,731 - INFO - ✅ تمت المزامنة بنجاح في المحاولة 1
2025-07-15 20:25:16,731 - INFO - ✅ تمت المزامنة بنجاح بعد تطبيق القالب
2025-07-15 20:25:16,732 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 20:25:16,736 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 20:25:16,745 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 20:25:16,755 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 20:25:16,799 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 20:25:16,802 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 20:25:16,802 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 20:25:16,802 - INFO - ⏳ التحقق من جاهزية الواجهة قبل بدء التوليد...
2025-07-15 20:25:16,803 - INFO - ⏳ انتظار جاهزية الواجهة (حد أقصى 3 ثانية)...
2025-07-15 20:25:16,803 - INFO - ✅ الواجهة جاهزة بعد 0.00 ثانية
2025-07-15 20:25:16,807 - INFO - 🔄 بدء فرض المزامنة الكاملة للواجهة...
2025-07-15 20:25:16,910 - INFO - ✅ تمت المزامنة بنجاح في المحاولة 1
2025-07-15 20:25:16,911 - INFO - ✅ جميع متطلبات generate_all متوفرة ومتزامنة
2025-07-15 20:25:16,911 - INFO - 🏭 بدء التحقق النهائي وتوليد الكرت...
2025-07-15 20:25:17,112 - INFO - 🔍 فحص نهائي للمتطلبات قبل التوليد...
2025-07-15 20:25:17,112 - INFO - 🔄 بدء فرض المزامنة الكاملة للواجهة...
2025-07-15 20:25:17,213 - INFO - ✅ تمت المزامنة بنجاح في المحاولة 1
2025-07-15 20:25:17,214 - INFO - ✅ جميع متطلبات generate_all متوفرة ومتزامنة
2025-07-15 20:25:17,214 - INFO - ✅ الفحص النهائي ناجح، بدء التوليد...
2025-07-15 20:25:17,220 - INFO - تم توليد 1 حساب
2025-07-15 20:25:17,221 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 208 (من generate_all)
2025-07-15 20:25:17,226 - ERROR - ❌ خطأ في استدعاء generate_all: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 20:25:17,230 - ERROR - 🔍 تشخيص: الخطأ مرتبط بعنصر GUI غير صالح
2025-07-15 20:25:17,230 - INFO - 🔄 بدء المحاولة الإضافية المحسنة...
2025-07-15 20:25:17,248 - INFO - 🔄 المحاولة الإضافية رقم 1/3
2025-07-15 20:25:17,957 - INFO - 🔄 بدء فرض المزامنة الكاملة للواجهة...
2025-07-15 20:25:18,057 - INFO - ✅ تمت المزامنة بنجاح في المحاولة 1
2025-07-15 20:25:18,058 - INFO - 🔄 بدء فرض المزامنة الكاملة للواجهة...
2025-07-15 20:25:18,160 - INFO - ✅ تمت المزامنة بنجاح في المحاولة 1
2025-07-15 20:25:18,161 - INFO - ✅ جميع متطلبات generate_all متوفرة ومتزامنة
2025-07-15 20:25:18,161 - INFO - 📊 تقرير تشخيصي للمحاولة 1:
  ✅ version_combo: v6
  ✅ delay_entry: 100
  ✅ server_entry: all

2025-07-15 20:25:18,161 - INFO - 🎯 تنفيذ generate_all في المحاولة 1
2025-07-15 20:25:18,468 - INFO - تم توليد 1 حساب
2025-07-15 20:25:18,469 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 209 (من generate_all)
2025-07-15 20:25:18,473 - ERROR - ❌ فشل تنفيذ generate_all في المحاولة 1: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 20:25:18,476 - INFO - 🔄 المحاولة الإضافية رقم 2/3
2025-07-15 20:25:19,678 - INFO - 🔄 بدء فرض المزامنة الكاملة للواجهة...
2025-07-15 20:25:19,779 - INFO - ✅ تمت المزامنة بنجاح في المحاولة 1
2025-07-15 20:25:19,779 - INFO - 🔄 بدء فرض المزامنة الكاملة للواجهة...
2025-07-15 20:25:19,880 - INFO - ✅ تمت المزامنة بنجاح في المحاولة 1
2025-07-15 20:25:19,881 - INFO - ✅ جميع متطلبات generate_all متوفرة ومتزامنة
2025-07-15 20:25:19,881 - INFO - 📊 تقرير تشخيصي للمحاولة 2:
  ✅ version_combo: v6
  ✅ delay_entry: 100
  ✅ server_entry: all

2025-07-15 20:25:19,881 - INFO - 🎯 تنفيذ generate_all في المحاولة 2
2025-07-15 20:25:20,187 - INFO - تم توليد 1 حساب
2025-07-15 20:25:20,188 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 210 (من generate_all)
2025-07-15 20:25:20,193 - ERROR - ❌ فشل تنفيذ generate_all في المحاولة 2: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 20:25:20,193 - INFO - 🔄 المحاولة الإضافية رقم 3/3
2025-07-15 20:25:21,895 - INFO - 🔄 بدء فرض المزامنة الكاملة للواجهة...
2025-07-15 20:25:21,997 - INFO - ✅ تمت المزامنة بنجاح في المحاولة 1
2025-07-15 20:25:21,998 - INFO - 🔄 بدء فرض المزامنة الكاملة للواجهة...
2025-07-15 20:25:22,100 - INFO - ✅ تمت المزامنة بنجاح في المحاولة 1
2025-07-15 20:25:22,100 - INFO - ✅ جميع متطلبات generate_all متوفرة ومتزامنة
2025-07-15 20:25:22,101 - INFO - 📊 تقرير تشخيصي للمحاولة 3:
  ✅ version_combo: v6
  ✅ delay_entry: 100
  ✅ server_entry: all

2025-07-15 20:25:22,101 - INFO - 🎯 تنفيذ generate_all في المحاولة 3
2025-07-15 20:25:22,406 - INFO - تم توليد 1 حساب
2025-07-15 20:25:22,407 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 211 (من generate_all)
2025-07-15 20:25:22,412 - ERROR - ❌ فشل تنفيذ generate_all في المحاولة 3: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 20:25:22,412 - ERROR - ❌ فشلت جميع المحاولات الإضافية (3 محاولات)
2025-07-15 20:25:48,133 - INFO - معالجة callback: cards_template_1_10
2025-07-15 20:25:48,355 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 20:25:48,618 - INFO - 🔄 إغلاق نافذة User Manager والانتقال إلى Hotspot...
2025-07-15 20:25:48,618 - INFO - 🚀 تم بدء عملية إنشاء الكرت مع إغلاق User Manager والانتقال لـ Hotspot
2025-07-15 20:25:48,619 - INFO - النظام hotspot مفعل بالفعل
2025-07-15 20:25:48,620 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-15 20:25:48,621 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-15 20:25:48,705 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 20:25:48,708 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 20:25:48,708 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 20:25:48,770 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 20:25:48,771 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 20:25:48,791 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 20:25:48,792 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 20:25:48,795 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 20:25:48,798 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 20:25:48,799 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 20:25:49,101 - INFO - 🔄 فرض مزامنة إضافية بعد تطبيق القالب...
2025-07-15 20:25:49,101 - INFO - 🔄 بدء فرض المزامنة الكاملة للواجهة...
2025-07-15 20:25:49,204 - INFO - ✅ تمت المزامنة بنجاح في المحاولة 1
2025-07-15 20:25:49,204 - INFO - ✅ تمت المزامنة بنجاح بعد تطبيق القالب
2025-07-15 20:25:49,205 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 20:25:49,209 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 20:25:49,240 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 20:25:49,262 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 20:25:49,264 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 20:25:49,265 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 20:25:49,266 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 20:25:49,266 - INFO - ⏳ التحقق من جاهزية الواجهة قبل بدء التوليد...
2025-07-15 20:25:49,267 - INFO - ⏳ انتظار جاهزية الواجهة (حد أقصى 3 ثانية)...
2025-07-15 20:25:49,267 - INFO - ✅ الواجهة جاهزة بعد 0.00 ثانية
2025-07-15 20:25:49,267 - INFO - 🔄 بدء فرض المزامنة الكاملة للواجهة...
2025-07-15 20:25:49,368 - INFO - ✅ تمت المزامنة بنجاح في المحاولة 1
2025-07-15 20:25:49,369 - INFO - ✅ جميع متطلبات generate_all متوفرة ومتزامنة
2025-07-15 20:25:49,369 - INFO - 🏭 بدء التحقق النهائي وتوليد الكرت...
2025-07-15 20:25:49,570 - INFO - 🔍 فحص نهائي للمتطلبات قبل التوليد...
2025-07-15 20:25:49,570 - INFO - 🔄 بدء فرض المزامنة الكاملة للواجهة...
2025-07-15 20:25:49,672 - INFO - ✅ تمت المزامنة بنجاح في المحاولة 1
2025-07-15 20:25:49,672 - INFO - ✅ جميع متطلبات generate_all متوفرة ومتزامنة
2025-07-15 20:25:49,672 - INFO - ✅ الفحص النهائي ناجح، بدء التوليد...
2025-07-15 20:25:49,677 - INFO - تم توليد 1 حساب
2025-07-15 20:25:49,678 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 212 (من generate_all)
2025-07-15 20:25:49,704 - ERROR - ❌ خطأ في استدعاء generate_all: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 20:25:49,705 - ERROR - 🔍 تشخيص: الخطأ مرتبط بعنصر GUI غير صالح
2025-07-15 20:25:49,705 - INFO - 🔄 بدء المحاولة الإضافية المحسنة...
2025-07-15 20:25:49,705 - INFO - 🔄 المحاولة الإضافية رقم 1/3
2025-07-15 20:25:50,408 - INFO - 🔄 بدء فرض المزامنة الكاملة للواجهة...
2025-07-15 20:25:50,509 - INFO - ✅ تمت المزامنة بنجاح في المحاولة 1
2025-07-15 20:25:50,509 - INFO - 🔄 بدء فرض المزامنة الكاملة للواجهة...
2025-07-15 20:25:50,610 - INFO - ✅ تمت المزامنة بنجاح في المحاولة 1
2025-07-15 20:25:50,611 - INFO - ✅ جميع متطلبات generate_all متوفرة ومتزامنة
2025-07-15 20:25:50,611 - INFO - 📊 تقرير تشخيصي للمحاولة 1:
  ✅ version_combo: v6
  ✅ delay_entry: 100
  ✅ server_entry: all

2025-07-15 20:25:50,611 - INFO - 🎯 تنفيذ generate_all في المحاولة 1
2025-07-15 20:25:50,917 - INFO - تم توليد 1 حساب
2025-07-15 20:25:50,917 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 213 (من generate_all)
2025-07-15 20:25:50,922 - ERROR - ❌ فشل تنفيذ generate_all في المحاولة 1: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 20:25:50,923 - INFO - 🔄 المحاولة الإضافية رقم 2/3
2025-07-15 20:25:52,124 - INFO - 🔄 بدء فرض المزامنة الكاملة للواجهة...
2025-07-15 20:25:52,225 - INFO - ✅ تمت المزامنة بنجاح في المحاولة 1
2025-07-15 20:25:52,226 - INFO - 🔄 بدء فرض المزامنة الكاملة للواجهة...
2025-07-15 20:25:52,327 - INFO - ✅ تمت المزامنة بنجاح في المحاولة 1
2025-07-15 20:25:52,327 - INFO - ✅ جميع متطلبات generate_all متوفرة ومتزامنة
2025-07-15 20:25:52,328 - INFO - 📊 تقرير تشخيصي للمحاولة 2:
  ✅ version_combo: v6
  ✅ delay_entry: 100
  ✅ server_entry: all

2025-07-15 20:25:52,328 - INFO - 🎯 تنفيذ generate_all في المحاولة 2
2025-07-15 20:25:52,633 - INFO - تم توليد 1 حساب
2025-07-15 20:25:52,634 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 214 (من generate_all)
2025-07-15 20:25:52,639 - ERROR - ❌ فشل تنفيذ generate_all في المحاولة 2: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 20:25:52,639 - INFO - 🔄 المحاولة الإضافية رقم 3/3
2025-07-15 20:25:54,340 - INFO - 🔄 بدء فرض المزامنة الكاملة للواجهة...
2025-07-15 20:25:54,441 - INFO - ✅ تمت المزامنة بنجاح في المحاولة 1
2025-07-15 20:25:54,442 - INFO - 🔄 بدء فرض المزامنة الكاملة للواجهة...
2025-07-15 20:25:54,543 - INFO - ✅ تمت المزامنة بنجاح في المحاولة 1
2025-07-15 20:25:54,543 - INFO - ✅ جميع متطلبات generate_all متوفرة ومتزامنة
2025-07-15 20:25:54,544 - INFO - 📊 تقرير تشخيصي للمحاولة 3:
  ✅ version_combo: v6
  ✅ delay_entry: 100
  ✅ server_entry: all

2025-07-15 20:25:54,544 - INFO - 🎯 تنفيذ generate_all في المحاولة 3
2025-07-15 20:25:54,850 - INFO - تم توليد 1 حساب
2025-07-15 20:25:54,851 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 215 (من generate_all)
2025-07-15 20:25:54,855 - ERROR - ❌ فشل تنفيذ generate_all في المحاولة 3: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 20:25:54,856 - ERROR - ❌ فشلت جميع المحاولات الإضافية (3 محاولات)
2025-07-15 20:26:08,275 - INFO - بدء إغلاق التطبيق
2025-07-15 20:26:08,276 - ERROR - خطأ أثناء إغلاق التطبيق: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 20:26:10,502 - INFO - تم بدء تشغيل التطبيق
2025-07-15 20:26:10,538 - INFO - تم إنشاء المجلدات الأساسية
2025-07-15 20:26:10,541 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-15 20:26:10,542 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-15 20:26:10,644 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-15 20:26:11,059 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-15 20:26:11,059 - INFO - تم إعداد التطبيق بنجاح
2025-07-15 20:26:13,061 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-15 20:26:13,284 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-15 20:26:13,284 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-15 20:26:13,286 - INFO - بدء إغلاق التطبيق
2025-07-15 20:26:13,287 - ERROR - خطأ أثناء إغلاق التطبيق: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-15 20:27:28,161 - INFO - تم بدء تشغيل التطبيق
2025-07-15 20:27:28,193 - INFO - تم إنشاء المجلدات الأساسية
2025-07-15 20:27:28,198 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-15 20:27:28,199 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-15 20:27:28,301 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-15 20:27:28,736 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-15 20:27:28,738 - INFO - تم إعداد التطبيق بنجاح
2025-07-15 20:27:30,744 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-15 20:27:31,164 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-15 20:27:31,166 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-15 20:27:34,173 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 20:27:34,175 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 20:27:34,415 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 20:27:34,416 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 20:27:37,426 - INFO - معالجة callback: select_system_um
2025-07-15 20:27:37,914 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-15 20:27:38,972 - INFO - معالجة callback: independent_template_um_10
2025-07-15 20:27:39,445 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-15 20:27:39,446 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-15 20:27:39,864 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-15 20:27:39,884 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-15 20:27:39,985 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-15 20:27:39,985 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-15 20:27:39,986 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-15 20:27:39,988 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-15 20:27:39,990 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-15 20:27:40,388 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-15 20:27:40,560 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 20:27:40,561 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 20:27:40,774 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-15 20:27:40,775 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-15 20:27:40,841 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-15 20:27:40,844 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-15 20:27:40,903 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-15 20:27:41,037 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 20:27:41,038 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 20:27:41,089 - INFO - 🖼️ محاولة تحميل صورة الخلفية: img\100100_1.jpg
2025-07-15 20:27:41,160 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-15 20:27:41,160 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-15 20:27:41,161 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 20:27:41,161 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 20:27:41,253 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-15 20:27:41,322 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 20:27:41,325 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 20:27:41,396 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 20:27:41,472 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 20:27:41,478 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-15 20:27:41,762 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-15 20:27:41,763 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-15 20:27:41,861 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-15 20:27:42,074 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-15 20:27:42,075 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-15 20:27:46,970 - INFO - معالجة الأمر: /start
2025-07-15 20:27:47,309 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 20:27:48,700 - INFO - معالجة callback: single_card
2025-07-15 20:27:48,913 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-15 20:27:48,914 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-15 20:27:49,220 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-15 20:27:50,365 - INFO - معالجة callback: card_count_1
2025-07-15 20:27:50,602 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-15 20:27:50,874 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-15 20:27:53,959 - INFO - معالجة callback: cards_template_1_10
2025-07-15 20:27:54,241 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 20:27:54,558 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-15 20:27:54,561 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-15 20:27:54,562 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-15 20:27:54,667 - INFO - تم حفظ الإعدادات قبل التبديل
2025-07-15 20:27:54,667 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-15 20:27:54,668 - INFO - ✅ تم تبديل النظام من user_manager إلى hotspot
2025-07-15 20:27:54,668 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-15 20:27:54,669 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-15 20:27:54,669 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-15 20:27:54,975 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-15 20:27:55,081 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 20:27:55,082 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 20:27:55,184 - ERROR - خطأ في إعادة بناء الواجهة: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 20:27:55,212 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-15 20:27:55,214 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-15 20:27:55,465 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 20:27:55,501 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 20:27:55,502 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 20:27:55,623 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 20:27:55,623 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 20:27:55,858 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 20:27:55,859 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 20:27:55,861 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 20:27:55,867 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 20:27:55,870 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 20:27:55,871 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 20:27:55,876 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 20:27:55,888 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 20:27:55,891 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 20:27:55,892 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 20:27:55,893 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 20:27:55,893 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 20:27:55,893 - INFO - 🏭 بدء توليد الكرت...
2025-07-15 20:27:55,970 - INFO - تم توليد 1 حساب
2025-07-15 20:27:55,972 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 208 (من generate_all)
2025-07-15 20:27:55,980 - ERROR - ❌ خطأ في تنفيذ إنشاء كرت واحد: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 20:28:06,659 - INFO - معالجة callback: cards_template_1_10
2025-07-15 20:28:06,896 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 20:28:07,182 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-15 20:28:07,185 - INFO - النظام hotspot مفعل بالفعل
2025-07-15 20:28:07,185 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-15 20:28:07,186 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-15 20:28:07,212 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 20:28:07,217 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 20:28:07,217 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 20:28:07,238 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 20:28:07,239 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 20:28:07,270 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 20:28:07,270 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 20:28:07,275 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 20:28:07,282 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 20:28:07,282 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 20:28:07,283 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 20:28:07,287 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 20:28:07,299 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 20:28:07,302 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 20:28:07,302 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 20:28:07,305 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 20:28:07,309 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 20:28:07,310 - INFO - 🏭 بدء توليد الكرت...
2025-07-15 20:28:07,316 - INFO - تم توليد 1 حساب
2025-07-15 20:28:07,317 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 209 (من generate_all)
2025-07-15 20:28:07,323 - ERROR - ❌ خطأ في تنفيذ إنشاء كرت واحد: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 20:28:19,047 - INFO - تم اختيار النظام: hotspot
2025-07-15 20:28:19,244 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-15 20:28:19,244 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 20:28:19,245 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 20:28:19,348 - ERROR - خطأ في اختيار النظام: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 20:28:28,008 - INFO - معالجة callback: cards_template_1_10
2025-07-15 20:28:28,223 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 20:28:28,470 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-15 20:28:28,472 - INFO - النظام hotspot مفعل بالفعل
2025-07-15 20:28:28,472 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-15 20:28:28,474 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-15 20:28:28,519 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 20:28:28,520 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 20:28:28,520 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 20:28:28,535 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 20:28:28,549 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 20:28:28,642 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 20:28:28,644 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 20:28:28,662 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 20:28:28,705 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 20:28:28,706 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 20:28:28,706 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 20:28:28,710 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 20:28:28,744 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 20:28:28,749 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 20:28:28,751 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 20:28:28,751 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 20:28:28,755 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 20:28:28,756 - INFO - 🏭 بدء توليد الكرت...
2025-07-15 20:28:28,799 - INFO - تم توليد 1 حساب
2025-07-15 20:28:28,801 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 208 (من generate_all)
2025-07-15 20:28:28,807 - ERROR - ❌ خطأ في تنفيذ إنشاء كرت واحد: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 20:28:42,731 - INFO - معالجة الأمر: /start
2025-07-15 20:28:43,013 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 20:28:44,697 - INFO - معالجة callback: single_card
2025-07-15 20:28:44,922 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-15 20:28:44,923 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-15 20:28:45,212 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-15 20:28:47,120 - INFO - معالجة callback: card_count_1
2025-07-15 20:28:47,353 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-15 20:28:47,610 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-15 20:28:49,745 - INFO - معالجة callback: cards_template_1_10
2025-07-15 20:28:50,020 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 20:28:50,288 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-15 20:28:50,290 - INFO - النظام hotspot مفعل بالفعل
2025-07-15 20:28:50,291 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-15 20:28:50,291 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-15 20:28:50,327 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 20:28:50,328 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 20:28:50,328 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 20:28:50,356 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 20:28:50,357 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 20:28:50,383 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 20:28:50,383 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 20:28:50,387 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 20:28:50,393 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 20:28:50,393 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 20:28:50,395 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 20:28:50,401 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 20:28:50,413 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 20:28:50,416 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 20:28:50,416 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 20:28:50,417 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 20:28:50,417 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 20:28:50,418 - INFO - 🏭 بدء توليد الكرت...
2025-07-15 20:28:50,424 - INFO - تم توليد 1 حساب
2025-07-15 20:28:50,428 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 209 (من generate_all)
2025-07-15 20:28:50,437 - ERROR - ❌ خطأ في تنفيذ إنشاء كرت واحد: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-15 20:29:04,988 - INFO - معالجة الأمر: /restart
2025-07-15 20:29:04,989 - INFO - طلب إعادة تشغيل البرنامج من التلجرام
2025-07-15 20:29:07,703 - INFO - معالجة callback: confirm_restart
2025-07-15 20:29:07,923 - INFO - تم تأكيد إعادة تشغيل البرنامج من التلجرام
2025-07-15 20:29:07,923 - INFO - بدء عملية إعادة تشغيل البرنامج من التلجرام
2025-07-15 20:29:13,780 - INFO - تم تسجيل العملية: إعادة تشغيل من التلجرام - بدء عملية إعادة التشغيل
2025-07-15 20:29:20,022 - INFO - تم بدء تشغيل التطبيق
2025-07-15 20:29:20,047 - INFO - تم إنشاء المجلدات الأساسية
2025-07-15 20:29:20,048 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-15 20:29:20,073 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-15 20:29:20,178 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250715_202920.db
2025-07-15 20:29:20,194 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-15 20:29:20,581 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-15 20:29:20,602 - INFO - تم إعداد التطبيق بنجاح
2025-07-15 20:29:22,614 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-15 20:29:22,865 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-15 20:29:22,866 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-15 20:29:25,875 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 20:29:25,883 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 20:29:26,122 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 20:29:26,129 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 20:29:43,246 - INFO - معالجة callback: single_card
2025-07-15 20:29:43,513 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-15 20:29:43,515 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-15 20:29:43,760 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-15 20:29:48,656 - INFO - معالجة callback: card_count_1
2025-07-15 20:29:48,878 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-15 20:29:49,160 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-15 20:29:50,799 - INFO - معالجة callback: cards_template_1_10
2025-07-15 20:29:51,077 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 20:29:51,340 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-15 20:29:51,342 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-15 20:29:51,354 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-15 20:29:51,455 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-15 20:29:51,456 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-15 20:29:51,456 - INFO - ✅ تم تبديل النظام من None إلى hotspot
2025-07-15 20:29:51,456 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-15 20:29:51,457 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-15 20:29:51,459 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-15 20:29:51,902 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-15 20:29:52,040 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 20:29:52,043 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 20:29:52,161 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-15 20:29:52,163 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-15 20:29:52,165 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-15 20:29:52,448 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 20:29:52,460 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 20:29:52,464 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 20:29:52,500 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 20:29:52,501 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 20:29:52,532 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 20:29:52,534 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 20:29:52,537 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 20:29:52,541 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 20:29:52,541 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 20:29:52,542 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 20:29:52,545 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 20:29:52,556 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 20:29:52,563 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 20:29:52,564 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 20:29:52,565 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 20:29:52,565 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 20:29:52,565 - INFO - 🏭 بدء توليد الكرت...
2025-07-15 20:29:52,572 - INFO - تم توليد 1 حساب
2025-07-15 20:29:52,572 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 208 (من generate_all)
2025-07-15 20:29:52,578 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-15 20:29:52,580 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-15 20:29:52,642 - INFO - نجح الاتصال مع *********
2025-07-15 20:29:52,750 - INFO - ✅ تم إرسال المستخدم: 02881321484
2025-07-15 20:29:52,751 - ERROR - ❌ كرت واحد: خطأ عام في الإرسال: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-15 20:29:52,752 - WARNING - ⚠️ فشل في الإرسال التلقائي، لكن الكرت تم توليده
2025-07-15 20:29:53,006 - INFO - ✅ تم إرسال تفاصيل 1 كرت عبر التلجرام بنجاح
2025-07-15 20:34:44,774 - INFO - بدء إغلاق التطبيق
2025-07-15 20:34:44,778 - INFO - تم إنشاء نسخة احتياطية: backups\mikrotik_cards_backup_20250715_203444.db
2025-07-15 20:34:44,779 - INFO - تم إنشاء نسخة احتياطية نهائية: backups\mikrotik_cards_backup_20250715_203444.db
2025-07-15 20:34:44,779 - INFO - تم قطع الاتصال مع MikroTik
2025-07-15 20:34:44,786 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-15 20:34:44,786 - INFO - تم إغلاق التطبيق بنجاح
2025-07-15 20:37:57,577 - INFO - تم بدء تشغيل التطبيق
2025-07-15 20:37:57,604 - INFO - تم إنشاء المجلدات الأساسية
2025-07-15 20:37:57,610 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-15 20:37:57,612 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-15 20:37:57,714 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-15 20:37:58,077 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-15 20:37:58,077 - INFO - تم إعداد التطبيق بنجاح
2025-07-15 20:38:00,082 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-15 20:38:00,413 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-15 20:38:00,414 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-15 20:38:03,425 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-15 20:38:03,427 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-15 20:38:03,709 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 20:38:03,709 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-15 20:38:05,365 - INFO - معالجة callback: select_system_hs
2025-07-15 20:38:05,918 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-15 20:38:07,194 - INFO - معالجة callback: independent_template_hs_10
2025-07-15 20:38:07,678 - INFO - 🔄 تبديل النظام تلقائياً من None إلى hotspot
2025-07-15 20:38:07,678 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → hotspot
2025-07-15 20:38:07,954 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-15 20:38:07,967 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-15 20:38:08,068 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-15 20:38:08,069 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-15 20:38:08,069 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-15 20:38:08,070 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-15 20:38:08,072 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-15 20:38:08,523 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-15 20:38:08,664 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-15 20:38:08,664 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-15 20:38:08,786 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-15 20:38:08,787 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-15 20:38:08,850 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-15 20:38:08,855 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى hotspot
2025-07-15 20:38:08,856 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-15 20:38:08,856 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 20:38:08,857 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 20:38:09,109 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 20:38:09,109 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 20:38:09,129 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 20:38:09,130 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 20:38:09,134 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 20:38:09,138 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 20:38:09,141 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 20:38:09,143 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 20:38:09,413 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-15 20:38:11,708 - INFO - معالجة الأمر: /start
2025-07-15 20:38:11,959 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-15 20:38:13,394 - INFO - معالجة callback: single_card
2025-07-15 20:38:13,627 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-15 20:38:13,627 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-15 20:38:13,878 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-15 20:38:14,949 - INFO - معالجة callback: card_count_1
2025-07-15 20:38:15,194 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-15 20:38:15,462 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-15 20:38:16,767 - INFO - معالجة callback: cards_template_1_10
2025-07-15 20:38:17,029 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-15 20:38:17,273 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-15 20:38:17,275 - INFO - النظام hotspot مفعل بالفعل
2025-07-15 20:38:17,275 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-15 20:38:17,276 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-15 20:38:17,315 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-15 20:38:17,317 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-15 20:38:17,317 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-15 20:38:17,334 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-15 20:38:17,335 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-15 20:38:17,359 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-15 20:38:17,359 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-15 20:38:17,362 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 20:38:17,365 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-15 20:38:17,365 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-15 20:38:17,366 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-15 20:38:17,371 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-15 20:38:17,384 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-15 20:38:17,388 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-15 20:38:17,388 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-15 20:38:17,389 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-15 20:38:17,390 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-15 20:38:17,390 - INFO - 🏭 بدء توليد الكرت...
2025-07-15 20:38:17,396 - INFO - تم توليد 1 حساب
2025-07-15 20:38:17,396 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 209 (من generate_all)
2025-07-15 20:38:17,403 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-15 20:38:17,405 - INFO - محاولة الاتصال بـ *********:8728 (عادي)
2025-07-15 20:38:17,468 - INFO - نجح الاتصال مع *********
2025-07-15 20:38:17,577 - INFO - ✅ تم إرسال المستخدم: 02920065491
2025-07-15 20:38:17,578 - ERROR - ❌ كرت واحد: خطأ عام في الإرسال: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-15 20:38:17,578 - WARNING - ⚠️ فشل في الإرسال التلقائي، لكن الكرت تم توليده
2025-07-15 20:38:17,831 - INFO - ✅ تم إرسال تفاصيل 1 كرت عبر التلجرام بنجاح
2025-07-15 20:38:29,774 - INFO - بدء إغلاق التطبيق
2025-07-15 20:38:29,777 - INFO - تم قطع الاتصال مع MikroTik
2025-07-15 20:38:29,781 - INFO - تم تسجيل العملية: إغلاق التطبيق - تم إغلاق التطبيق بنجاح
2025-07-15 20:38:29,782 - INFO - تم إغلاق التطبيق بنجاح
