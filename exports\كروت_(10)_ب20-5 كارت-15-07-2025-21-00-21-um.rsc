# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-15 21:00:21
# القالب: 10
# النظام: user_manager
# عدد الكروت: 5
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 5";

:local success 0;
:local errors 0;
:local total 5;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 5 مستخدم User Manager...";

# المستخدم 1: 2055657832
:do {
    /tool user-manager user add customer="admin" username="2055657832" password="80089380" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2055657832";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2055657832";
};

# المستخدم 2: 2046021938
:do {
    /tool user-manager user add customer="admin" username="2046021938" password="36389572" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2046021938";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2046021938";
};

# المستخدم 3: 2030469088
:do {
    /tool user-manager user add customer="admin" username="2030469088" password="79559428" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2030469088";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2030469088";
};

# المستخدم 4: 2084473577
:do {
    /tool user-manager user add customer="admin" username="2084473577" password="52867293" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2084473577";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2084473577";
};

# المستخدم 5: 2071700769
:do {
    /tool user-manager user add customer="admin" username="2071700769" password="16774199" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2071700769";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2071700769";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
