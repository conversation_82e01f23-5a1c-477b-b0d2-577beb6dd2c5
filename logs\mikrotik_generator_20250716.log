2025-07-16 00:07:16,571 - INFO - تم بدء تشغيل التطبيق
2025-07-16 00:07:16,822 - INFO - تم إنشاء المجلدات الأساسية
2025-07-16 00:07:16,852 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-16 00:07:16,852 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:07:16,954 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-16 00:07:18,016 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-16 00:07:18,016 - INFO - تم <PERSON>عداد التطبيق بنجاح
2025-07-16 00:07:20,016 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-16 00:07:20,321 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-16 00:07:20,322 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-16 00:07:23,327 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-16 00:07:23,363 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-16 00:07:23,774 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 00:07:23,775 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-16 00:08:22,528 - INFO - معالجة callback: select_system_um
2025-07-16 00:08:24,538 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-16 00:08:26,097 - INFO - معالجة callback: independent_template_um_10
2025-07-16 00:08:27,468 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-16 00:08:27,469 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-16 00:08:28,148 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-16 00:08:28,240 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:08:28,341 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-16 00:08:28,341 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-16 00:08:28,341 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-16 00:08:28,343 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-16 00:08:28,344 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:08:28,914 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-16 00:08:29,134 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-16 00:08:29,135 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:08:29,856 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-16 00:08:29,859 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-16 00:08:29,926 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-16 00:08:29,955 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-16 00:08:29,965 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-16 00:08:29,966 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:08:29,966 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:08:30,328 - INFO - 🖼️ محاولة تحميل صورة الخلفية: img\100100_1.jpg
2025-07-16 00:08:30,331 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-16 00:08:30,332 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-16 00:08:30,332 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:08:30,332 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:08:30,427 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-16 00:08:30,427 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:08:30,432 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:08:30,450 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:08:30,453 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:08:30,454 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-16 00:08:30,760 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-16 00:08:30,928 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:08:30,929 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:08:31,054 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:08:31,057 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:08:43,675 - INFO - معالجة الأمر: /start
2025-07-16 00:08:44,002 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 00:08:53,293 - INFO - معالجة callback: single_card
2025-07-16 00:08:53,563 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-16 00:08:53,564 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-16 00:08:53,828 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-16 00:09:14,464 - INFO - معالجة callback: card_count_1
2025-07-16 00:09:14,684 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-16 00:09:14,926 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-16 00:09:18,947 - INFO - معالجة callback: cards_template_1_10
2025-07-16 00:09:19,184 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-16 00:09:19,442 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-16 00:09:19,444 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-16 00:09:19,445 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:09:19,549 - INFO - تم حفظ الإعدادات قبل التبديل
2025-07-16 00:09:19,550 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-16 00:09:19,550 - INFO - ✅ تم تبديل النظام من user_manager إلى hotspot
2025-07-16 00:09:19,550 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-16 00:09:19,551 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-16 00:09:19,551 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:09:19,858 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-16 00:09:19,858 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-16 00:09:19,859 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:09:19,961 - WARNING - تحذير في تحميل الإعدادات: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:09:19,966 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-16 00:09:19,968 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-16 00:09:19,969 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-16 00:09:20,140 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-16 00:09:20,141 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:09:20,147 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:09:20,245 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:09:20,246 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:09:20,544 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-16 00:09:20,544 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:09:20,547 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:09:20,551 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:09:20,551 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-16 00:09:20,552 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-16 00:09:20,555 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-16 00:09:20,567 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-16 00:09:20,570 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:09:20,571 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-16 00:09:20,572 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-16 00:09:20,572 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-16 00:09:20,572 - INFO - 🏭 بدء توليد الكرت...
2025-07-16 00:09:20,622 - INFO - تم توليد 1 حساب
2025-07-16 00:09:20,623 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 6 (من generate_all)
2025-07-16 00:09:20,629 - ERROR - خطأ في توليد الكرت: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:09:20,629 - INFO - محاولة إعادة التوليد...
2025-07-16 00:09:20,635 - INFO - تم توليد 1 حساب
2025-07-16 00:09:20,635 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 7 (من generate_all)
2025-07-16 00:09:20,640 - ERROR - ❌ خطأ في تنفيذ إنشاء كرت واحد: فشل في توليد الكرت: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:10:09,133 - INFO - معالجة callback: cards_template_1_10
2025-07-16 00:10:09,377 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-16 00:10:09,684 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-16 00:10:09,686 - INFO - النظام hotspot مفعل بالفعل
2025-07-16 00:10:09,686 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-16 00:10:09,688 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-16 00:10:09,728 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-16 00:10:09,729 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:10:09,729 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:10:09,752 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:10:09,753 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:10:09,782 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-16 00:10:09,782 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:10:09,785 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:10:09,789 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:10:09,789 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-16 00:10:09,790 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-16 00:10:09,793 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-16 00:10:09,806 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-16 00:10:09,813 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:10:09,814 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-16 00:10:09,815 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-16 00:10:09,815 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-16 00:10:09,815 - INFO - 🏭 بدء توليد الكرت...
2025-07-16 00:10:09,822 - INFO - تم توليد 1 حساب
2025-07-16 00:10:09,824 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 8 (من generate_all)
2025-07-16 00:10:09,830 - ERROR - خطأ في توليد الكرت: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:10:09,830 - INFO - محاولة إعادة التوليد...
2025-07-16 00:10:09,841 - INFO - تم توليد 1 حساب
2025-07-16 00:10:09,843 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 9 (من generate_all)
2025-07-16 00:10:09,849 - ERROR - ❌ خطأ في تنفيذ إنشاء كرت واحد: فشل في توليد الكرت: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:10:14,860 - INFO - بدء إغلاق التطبيق
2025-07-16 00:10:14,861 - ERROR - خطأ أثناء إغلاق التطبيق: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:28:29,212 - INFO - تم بدء تشغيل التطبيق
2025-07-16 00:28:29,402 - INFO - تم إنشاء المجلدات الأساسية
2025-07-16 00:28:29,484 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-16 00:28:29,484 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:28:30,318 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-16 00:28:31,972 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-16 00:28:31,973 - INFO - تم إعداد التطبيق بنجاح
2025-07-16 00:28:34,013 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-16 00:28:34,593 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-16 00:28:34,594 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-16 00:28:35,031 - INFO - معالجة الأمر: /start
2025-07-16 00:28:35,554 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 00:28:37,598 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-16 00:28:37,599 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-16 00:28:38,054 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 00:28:38,054 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-16 00:28:44,946 - INFO - معالجة callback: select_system_um
2025-07-16 00:28:45,489 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-16 00:28:46,554 - INFO - معالجة callback: independent_template_um_10
2025-07-16 00:28:47,064 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-16 00:28:47,064 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-16 00:28:47,325 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-16 00:28:47,399 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:28:47,500 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-16 00:28:47,501 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-16 00:28:47,501 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-16 00:28:47,503 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-16 00:28:47,504 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:28:48,029 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-16 00:28:48,193 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-16 00:28:48,194 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:28:48,979 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-16 00:28:48,979 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-16 00:28:49,043 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-16 00:28:49,062 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-16 00:28:49,063 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-16 00:28:49,063 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:28:49,063 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:28:49,386 - INFO - 🖼️ محاولة تحميل صورة الخلفية: img\100100_1.jpg
2025-07-16 00:28:49,392 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-16 00:28:49,392 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-16 00:28:49,392 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:28:49,393 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:28:49,503 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-16 00:28:49,503 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:28:49,506 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:28:49,517 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:28:49,520 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:28:49,522 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-16 00:28:49,803 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-16 00:28:50,006 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:28:50,011 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:28:50,119 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:28:50,120 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:28:55,594 - INFO - معالجة الأمر: /start
2025-07-16 00:28:55,883 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 00:28:59,765 - INFO - معالجة callback: single_card
2025-07-16 00:29:00,023 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-16 00:29:00,026 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-16 00:29:00,369 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-16 00:29:08,398 - INFO - معالجة callback: card_count_1
2025-07-16 00:29:08,677 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-16 00:29:08,996 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-16 00:29:10,509 - INFO - معالجة callback: cards_template_1_10
2025-07-16 00:29:10,854 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-16 00:29:11,266 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-16 00:29:11,267 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-16 00:29:11,268 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:29:11,372 - INFO - تم حفظ الإعدادات قبل التبديل
2025-07-16 00:29:11,372 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-16 00:29:11,373 - INFO - ✅ تم تبديل النظام من user_manager إلى hotspot
2025-07-16 00:29:11,373 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-16 00:29:11,373 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-16 00:29:11,374 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:29:11,679 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-16 00:29:11,682 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-16 00:29:11,682 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:29:11,785 - WARNING - تحذير في تحميل الإعدادات: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:29:11,789 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-16 00:29:11,791 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-16 00:29:11,791 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-16 00:29:11,956 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-16 00:29:12,011 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:29:12,014 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:29:12,037 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:29:12,038 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:29:12,344 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-16 00:29:12,564 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:29:12,568 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:29:12,573 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:29:12,614 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-16 00:29:12,624 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-16 00:29:12,628 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-16 00:29:12,640 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-16 00:29:12,696 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:29:12,696 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-16 00:29:12,698 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-16 00:29:12,698 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-16 00:29:12,698 - INFO - 🏭 بدء توليد الكرت...
2025-07-16 00:29:12,717 - INFO - تم توليد 1 حساب
2025-07-16 00:29:12,723 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 6 (من generate_all)
2025-07-16 00:29:12,729 - WARNING - تحذير في حفظ الإعدادات: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:29:12,734 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-16 00:29:12,734 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-16 00:29:12,798 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-16 00:29:12,919 - INFO - ✅ تم إرسال المستخدم: 02572797237
2025-07-16 00:29:12,919 - ERROR - ❌ كرت واحد: خطأ عام في الإرسال: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-16 00:29:12,919 - WARNING - ⚠️ فشل في الإرسال التلقائي، لكن الكرت تم توليده
2025-07-16 00:29:13,172 - INFO - ✅ تم إرسال تفاصيل 1 كرت عبر التلجرام بنجاح
2025-07-16 00:29:27,731 - INFO - معالجة الأمر: /start
2025-07-16 00:29:28,001 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 00:29:33,568 - INFO - معالجة callback: select_system_um
2025-07-16 00:29:34,057 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-16 00:29:36,408 - INFO - معالجة callback: independent_template_um_10
2025-07-16 00:29:36,914 - INFO - 🔄 تبديل النظام تلقائياً من hotspot إلى user_manager
2025-07-16 00:29:36,914 - INFO - 🔄 طلب تبديل النظام من التلجرام: hotspot → user_manager
2025-07-16 00:29:37,375 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-16 00:29:37,376 - INFO - ✅ تم قطع الاتصال الموجود بنجاح
2025-07-16 00:29:37,376 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:29:37,482 - WARNING - تحذير: فشل في حفظ الإعدادات: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:29:37,482 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-16 00:29:37,483 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-16 00:29:37,484 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-16 00:29:37,485 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:29:37,752 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-16 00:29:37,776 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-16 00:29:37,777 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:29:37,961 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-16 00:29:37,963 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-16 00:29:37,982 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-16 00:29:37,989 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من hotspot إلى user_manager
2025-07-16 00:29:37,990 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-16 00:29:37,990 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:29:37,991 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:29:38,198 - INFO - 🖼️ محاولة تحميل صورة الخلفية: img\100100_1.jpg
2025-07-16 00:29:38,199 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-16 00:29:38,200 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-16 00:29:38,200 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:29:38,200 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:29:38,298 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-16 00:29:38,298 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:29:38,310 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:29:38,321 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:29:38,324 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:29:38,326 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-16 00:29:38,595 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-16 00:29:38,804 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:29:38,807 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:29:38,927 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:29:38,928 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:29:41,570 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-16 00:29:43,329 - INFO - معالجة callback: independent_count_um_10_lightning_5
2025-07-16 00:29:43,839 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:29:43,840 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:29:43,854 - INFO - 🖼️ محاولة تحميل صورة الخلفية: img\100100_1.jpg
2025-07-16 00:29:43,855 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-16 00:29:43,855 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-16 00:29:43,856 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:29:43,856 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:29:43,949 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-16 00:29:43,950 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:29:43,953 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:29:43,969 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:29:44,308 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:29:44,309 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:29:44,324 - INFO - 🖼️ محاولة تحميل صورة الخلفية: img\100100_1.jpg
2025-07-16 00:29:44,325 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-16 00:29:44,325 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-16 00:29:44,326 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:29:44,326 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:29:44,421 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-16 00:29:44,421 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:29:44,423 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:29:44,446 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:29:44,448 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 1801
2025-07-16 00:29:44,450 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:29:44,453 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:29:44,465 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-16 00:29:44,466 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-16 00:29:44,466 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-16 00:29:44,466 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 5
2025-07-16 00:29:44,467 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 5 حساب
2025-07-16 00:29:44,467 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 5
2025-07-16 00:29:44,467 - INFO - ⚡ البرق للتلجرام: تم توليد 5 حساب
2025-07-16 00:29:44,467 - INFO - ⚡ البرق للتلجرام: تنفيذ مجدول لـ 5 كارت (حد التقسيم: 100)
2025-07-16 00:29:44,509 - INFO - ⚡ البرق للتلجرام: تخطي حفظ الملفات المحلية - استخدام الجدولة المؤقتة
2025-07-16 00:29:44,510 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-16 00:29:44,588 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-16 00:29:44,845 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:29:44,846 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:29:44,870 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:29:44,873 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:29:44,937 - INFO - ⚡ تم إضافة السكريبت: telegram_lightning_user_manager_20250716_002944
2025-07-16 00:29:44,978 - INFO - ⚡ وقت MikroTik الحالي: 21:29:44
2025-07-16 00:29:45,101 - INFO - ⚡ سيتم تنفيذ السكريبت في: 21:29:47 (بعد 3 ثواني من وقت MikroTik)
2025-07-16 00:29:45,102 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:29:45,103 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:29:45,160 - INFO - ⚡ تم إنشاء الجدولة المؤقتة: telegram_schedule_20250716_002944 للتنفيذ في 21:29:47
2025-07-16 00:29:45,162 - INFO - ⚡ تم إعداد البرق المجدول للتلجرام بنجاح - السكريبت: telegram_lightning_user_manager_20250716_002944, الجدولة: telegram_schedule_20250716_002944
2025-07-16 00:29:45,163 - INFO - ⚡ سيتم تنفيذ السكريبت في 21:29:47 مع تنظيف تلقائي بعد الانتهاء
2025-07-16 00:29:45,164 - INFO - ⚡ البرق للتلجرام مكتمل: تم جدولة إنشاء وإرسال 5 كارت بنجاح (مع تنظيف تلقائي)
2025-07-16 00:29:47,782 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 5
2025-07-16 00:29:47,926 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-5 كارت-16-07-2025-00-29-47-um.pdf
2025-07-16 00:29:47,945 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-5 كارت-16-07-2025-00-29-47-um.rsc
2025-07-16 00:29:50,225 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-5 كارت-16-07-2025-00-29-47-um.pdf
2025-07-16 00:29:50,471 - INFO - تم إرسال 5 كرت عبر التلجرام باستخدام قالب 10
2025-07-16 00:29:58,508 - INFO - معالجة الأمر: /start
2025-07-16 00:29:58,762 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 00:30:00,396 - INFO - معالجة callback: select_system_hs
2025-07-16 00:30:01,017 - INFO - تم إرسال 5 قالب مفلتر لنظام Hotspot
2025-07-16 00:30:02,312 - INFO - معالجة callback: independent_template_hs_10
2025-07-16 00:30:02,789 - INFO - 🔄 تبديل النظام تلقائياً من user_manager إلى hotspot
2025-07-16 00:30:02,789 - INFO - 🔄 طلب تبديل النظام من التلجرام: user_manager → hotspot
2025-07-16 00:30:03,100 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-16 00:30:03,100 - INFO - ✅ تم قطع الاتصال الموجود بنجاح
2025-07-16 00:30:03,101 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:30:03,208 - INFO - تم حفظ الإعدادات قبل التبديل
2025-07-16 00:30:03,209 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-16 00:30:03,209 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-16 00:30:03,213 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-16 00:30:03,214 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:30:03,497 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-16 00:30:03,497 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-16 00:30:03,498 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:30:03,600 - WARNING - تحذير في تحميل الإعدادات: invalid command name ".!canvas3.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:30:03,604 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-16 00:30:03,606 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-16 00:30:03,737 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-16 00:30:03,740 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من user_manager إلى hotspot
2025-07-16 00:30:03,752 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-16 00:30:03,753 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:30:03,764 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:30:03,809 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:30:03,820 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:30:04,118 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-16 00:30:04,121 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:30:04,124 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:30:04,130 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:30:04,133 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:30:04,134 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-16 00:30:04,468 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-16 00:30:11,470 - INFO - معالجة الأمر: /start
2025-07-16 00:30:11,782 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 00:30:13,275 - INFO - معالجة callback: single_card
2025-07-16 00:30:13,660 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-16 00:30:13,661 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-16 00:30:14,017 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-16 00:30:15,846 - INFO - معالجة callback: card_count_1
2025-07-16 00:30:16,070 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-16 00:30:16,335 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-16 00:30:17,424 - INFO - معالجة callback: cards_template_1_10
2025-07-16 00:30:17,666 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-16 00:30:17,951 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-16 00:30:17,952 - INFO - النظام hotspot مفعل بالفعل
2025-07-16 00:30:17,953 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-16 00:30:17,953 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-16 00:30:18,010 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-16 00:30:18,022 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:30:18,031 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:30:18,049 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:30:18,050 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:30:18,077 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-16 00:30:18,078 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:30:18,081 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:30:18,085 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:30:18,088 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-16 00:30:18,090 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-16 00:30:18,094 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-16 00:30:18,108 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-16 00:30:18,111 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:30:18,111 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-16 00:30:18,112 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-16 00:30:18,113 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-16 00:30:18,113 - INFO - 🏭 بدء توليد الكرت...
2025-07-16 00:30:18,122 - INFO - تم توليد 1 حساب
2025-07-16 00:30:18,127 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 6 (من generate_all)
2025-07-16 00:30:18,134 - WARNING - تحذير في حفظ الإعدادات: invalid command name ".!canvas3.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:30:18,135 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-16 00:30:18,135 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-16 00:30:18,234 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-16 00:30:18,322 - INFO - ✅ تم إرسال المستخدم: 02916176295
2025-07-16 00:30:18,322 - ERROR - ❌ كرت واحد: خطأ عام في الإرسال: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-16 00:30:18,322 - WARNING - ⚠️ فشل في الإرسال التلقائي، لكن الكرت تم توليده
2025-07-16 00:30:18,673 - INFO - ✅ تم إرسال تفاصيل 1 كرت عبر التلجرام بنجاح
2025-07-16 00:31:10,443 - INFO - معالجة الأمر: /start
2025-07-16 00:31:10,697 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 00:31:14,484 - INFO - معالجة callback: select_system_um
2025-07-16 00:31:14,951 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-16 00:31:16,002 - INFO - معالجة callback: independent_template_um_10
2025-07-16 00:31:16,511 - INFO - 🔄 تبديل النظام تلقائياً من hotspot إلى user_manager
2025-07-16 00:31:16,511 - INFO - 🔄 طلب تبديل النظام من التلجرام: hotspot → user_manager
2025-07-16 00:31:16,780 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-16 00:31:16,781 - INFO - ✅ تم قطع الاتصال الموجود بنجاح
2025-07-16 00:31:16,781 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:31:16,885 - WARNING - تحذير: فشل في حفظ الإعدادات: invalid command name ".!canvas3.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:31:16,886 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-16 00:31:16,886 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-16 00:31:16,888 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-16 00:31:16,888 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:31:17,190 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-16 00:31:17,217 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-16 00:31:17,217 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:31:17,413 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-16 00:31:17,414 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-16 00:31:17,580 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-16 00:31:17,582 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من hotspot إلى user_manager
2025-07-16 00:31:17,584 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-16 00:31:17,585 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:31:17,588 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:31:17,649 - INFO - 🖼️ محاولة تحميل صورة الخلفية: img\100100_1.jpg
2025-07-16 00:31:17,649 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-16 00:31:17,650 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-16 00:31:17,650 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:31:17,650 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:31:17,772 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-16 00:31:17,773 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:31:17,775 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:31:17,788 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:31:17,791 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:31:17,792 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-16 00:31:18,109 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-16 00:31:18,278 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:31:18,279 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:31:18,388 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:31:18,389 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:31:26,392 - INFO - معالجة callback: independent_create_um_10_lightning
2025-07-16 00:31:36,356 - INFO - معالجة callback: independent_count_um_10_lightning_10
2025-07-16 00:31:41,212 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:31:41,212 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:31:41,226 - INFO - 🖼️ محاولة تحميل صورة الخلفية: img\100100_1.jpg
2025-07-16 00:31:41,227 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-16 00:31:41,227 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-16 00:31:41,227 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:31:41,228 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:31:41,343 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-16 00:31:41,343 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:31:41,346 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:31:41,359 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:31:41,849 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:31:41,850 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:31:42,069 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:31:42,070 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:31:42,902 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:31:42,902 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:31:42,916 - INFO - 🖼️ محاولة تحميل صورة الخلفية: img\100100_1.jpg
2025-07-16 00:31:42,917 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-16 00:31:42,917 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-16 00:31:42,917 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:31:42,918 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:31:43,033 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-16 00:31:43,033 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:31:43,036 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:31:43,049 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:31:43,051 - INFO - ⚡ البرق: تم الحفاظ على العدد المحدد: 1801
2025-07-16 00:31:43,051 - INFO - تم تطبيق إعدادات القالب '10' بنجاح
2025-07-16 00:31:43,052 - INFO - ⚡ استخدام البرق للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-16 00:31:43,052 - INFO - ⚡ بدء البرق التلقائي للتلجرام (مع جدولة مؤقتة وتنظيف تلقائي)
2025-07-16 00:31:43,052 - INFO - ⚡ البرق: استخدام العدد من التلجرام بوت: 10
2025-07-16 00:31:43,052 - INFO - ⚡ البرق للتلجرام: العدد المطلوب 10 حساب
2025-07-16 00:31:43,052 - INFO - ⚡ البرق: تم تحديث عدد الحسابات في الواجهة إلى: 10
2025-07-16 00:31:43,053 - INFO - ⚡ البرق للتلجرام: تم توليد 10 حساب
2025-07-16 00:31:43,053 - INFO - ⚡ البرق للتلجرام: تنفيذ مجدول لـ 10 كارت (حد التقسيم: 100)
2025-07-16 00:31:43,053 - INFO - ⚡ البرق للتلجرام: تخطي حفظ الملفات المحلية - استخدام الجدولة المؤقتة
2025-07-16 00:31:43,053 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-16 00:31:43,120 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-16 00:31:43,174 - INFO - ⚡ تم إضافة السكريبت: telegram_lightning_user_manager_20250716_003143
2025-07-16 00:31:43,210 - INFO - ⚡ وقت MikroTik الحالي: 21:31:42
2025-07-16 00:31:43,211 - INFO - ⚡ سيتم تنفيذ السكريبت في: 21:31:45 (بعد 3 ثواني من وقت MikroTik)
2025-07-16 00:31:43,262 - INFO - ⚡ تم إنشاء الجدولة المؤقتة: telegram_schedule_20250716_003143 للتنفيذ في 21:31:45
2025-07-16 00:31:43,265 - INFO - ⚡ تم إعداد البرق المجدول للتلجرام بنجاح - السكريبت: telegram_lightning_user_manager_20250716_003143, الجدولة: telegram_schedule_20250716_003143
2025-07-16 00:31:43,265 - INFO - ⚡ سيتم تنفيذ السكريبت في 21:31:45 مع تنظيف تلقائي بعد الانتهاء
2025-07-16 00:31:43,265 - INFO - ⚡ البرق للتلجرام مكتمل: تم جدولة إنشاء وإرسال 10 كارت بنجاح (مع تنظيف تلقائي)
2025-07-16 00:31:43,410 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:31:43,411 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:31:43,541 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:31:43,542 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:31:46,087 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 10
2025-07-16 00:31:46,149 - INFO - تم إنشاء PDF بنجاح: exports/كروت_(10)_ب20-10 كارت-16-07-2025-00-31-46-um.pdf
2025-07-16 00:31:46,164 - INFO - تم حفظ ملف .rsc الاحتياطي: exports/كروت_(10)_ب20-10 كارت-16-07-2025-00-31-46-um.rsc
2025-07-16 00:31:46,888 - INFO - تم إرسال الملف بنجاح: كروت_(10)_ب20-10 كارت-16-07-2025-00-31-46-um.pdf
2025-07-16 00:31:47,414 - INFO - تم إرسال 10 كرت عبر التلجرام باستخدام قالب 10
2025-07-16 00:31:54,873 - INFO - معالجة الأمر: /start
2025-07-16 00:31:55,186 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 00:31:57,022 - INFO - معالجة callback: single_card
2025-07-16 00:31:57,262 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-16 00:31:57,263 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-16 00:31:57,525 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-16 00:31:58,739 - INFO - معالجة callback: card_count_1
2025-07-16 00:31:58,955 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-16 00:31:59,219 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-16 00:32:00,546 - INFO - معالجة callback: cards_template_1_10
2025-07-16 00:32:00,794 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-16 00:32:01,050 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-16 00:32:01,051 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-16 00:32:01,052 - INFO - ✅ تم قطع الاتصال الموجود بنجاح
2025-07-16 00:32:01,053 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:32:01,157 - INFO - تم حفظ الإعدادات قبل التبديل
2025-07-16 00:32:01,158 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-16 00:32:01,158 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-16 00:32:01,158 - INFO - ✅ تم تبديل النظام من user_manager إلى hotspot
2025-07-16 00:32:01,158 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-16 00:32:01,159 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:32:01,467 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-16 00:32:01,467 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-16 00:32:01,468 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:32:01,570 - WARNING - تحذير في تحميل الإعدادات: invalid command name ".!canvas5.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:32:01,577 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-16 00:32:01,577 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-16 00:32:01,579 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-16 00:32:01,771 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-16 00:32:01,773 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:32:01,790 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:32:01,849 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:32:01,850 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:32:02,133 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-16 00:32:02,133 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:32:02,136 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:32:02,141 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:32:02,141 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-16 00:32:02,142 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-16 00:32:02,145 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-16 00:32:02,157 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-16 00:32:02,160 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:32:02,160 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-16 00:32:02,164 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-16 00:32:02,164 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-16 00:32:02,165 - INFO - 🏭 بدء توليد الكرت...
2025-07-16 00:32:02,172 - INFO - تم توليد 1 حساب
2025-07-16 00:32:02,173 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 11 (من generate_all)
2025-07-16 00:32:02,178 - WARNING - تحذير في حفظ الإعدادات: invalid command name ".!canvas5.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:32:02,178 - INFO - 🚀 إرسال الكرت إلى MikroTik...
2025-07-16 00:32:02,178 - INFO - محاولة الاتصال بـ 6.6.6.100:8728 (عادي)
2025-07-16 00:32:02,241 - INFO - نجح الاتصال مع 6.6.6.100
2025-07-16 00:32:02,324 - INFO - ✅ تم إرسال المستخدم: 02531445019
2025-07-16 00:32:02,324 - ERROR - ❌ كرت واحد: خطأ عام في الإرسال: 'RouterOsApi' object has no attribute 'disconnect'
2025-07-16 00:32:02,324 - WARNING - ⚠️ فشل في الإرسال التلقائي، لكن الكرت تم توليده
2025-07-16 00:32:02,574 - INFO - ✅ تم إرسال تفاصيل 1 كرت عبر التلجرام بنجاح
2025-07-16 00:32:12,830 - INFO - بدء إغلاق التطبيق
2025-07-16 00:32:12,830 - ERROR - خطأ أثناء إغلاق التطبيق: invalid command name ".!canvas5.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
