# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-16 00:29:47
# القالب: 10
# النظام: user_manager
# عدد الكروت: 5
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 5";

:local success 0;
:local errors 0;
:local total 5;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 5 مستخدم User Manager...";

# المستخدم 1: 2071478010
:do {
    /tool user-manager user add customer="admin" username="2071478010" password="54800778" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2071478010";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2071478010";
};

# المستخدم 2: 2028238586
:do {
    /tool user-manager user add customer="admin" username="2028238586" password="20102782" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2028238586";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2028238586";
};

# المستخدم 3: 2065486992
:do {
    /tool user-manager user add customer="admin" username="2065486992" password="94025294" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2065486992";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2065486992";
};

# المستخدم 4: 2002721693
:do {
    /tool user-manager user add customer="admin" username="2002721693" password="81829939" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2002721693";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2002721693";
};

# المستخدم 5: 2041325616
:do {
    /tool user-manager user add customer="admin" username="2041325616" password="36904821" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2041325616";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2041325616";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
