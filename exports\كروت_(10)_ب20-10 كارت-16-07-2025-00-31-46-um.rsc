# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-16 00:31:46
# القالب: 10
# النظام: user_manager
# عدد الكروت: 10
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 10";

:local success 0;
:local errors 0;
:local total 10;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 10 مستخدم User Manager...";

# المستخدم 1: 2012478021
:do {
    /tool user-manager user add customer="admin" username="2012478021" password="52932182" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2012478021";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2012478021";
};

# المستخدم 2: 2030710986
:do {
    /tool user-manager user add customer="admin" username="2030710986" password="21630284" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2030710986";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2030710986";
};

# المستخدم 3: 2035561327
:do {
    /tool user-manager user add customer="admin" username="2035561327" password="47054500" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2035561327";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2035561327";
};

# المستخدم 4: 2085455932
:do {
    /tool user-manager user add customer="admin" username="2085455932" password="17658637" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2085455932";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2085455932";
};

# المستخدم 5: 2073862790
:do {
    /tool user-manager user add customer="admin" username="2073862790" password="43217589" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2073862790";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2073862790";
};

# المستخدم 6: 2099378674
:do {
    /tool user-manager user add customer="admin" username="2099378674" password="27529326" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2099378674";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2099378674";
};

# المستخدم 7: 2022438622
:do {
    /tool user-manager user add customer="admin" username="2022438622" password="30467622" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2022438622";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2022438622";
};

# المستخدم 8: 2056411555
:do {
    /tool user-manager user add customer="admin" username="2056411555" password="50282459" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056411555";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056411555";
};

# المستخدم 9: 2007185266
:do {
    /tool user-manager user add customer="admin" username="2007185266" password="94966829" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2007185266";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2007185266";
};

# المستخدم 10: 2083651520
:do {
    /tool user-manager user add customer="admin" username="2083651520" password="25387064" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2083651520";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2083651520";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
