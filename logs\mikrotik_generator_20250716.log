2025-07-16 00:07:16,571 - INFO - تم بدء تشغيل التطبيق
2025-07-16 00:07:16,822 - INFO - تم إنشاء المجلدات الأساسية
2025-07-16 00:07:16,852 - INFO - 🔄 بدء تشغيل البرنامج - قطع أي اتصالات موجودة...
2025-07-16 00:07:16,852 - INFO - ✅ بدء تشغيل البرنامج - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:07:16,954 - INFO - تم إعداد قاعدة البيانات بنجاح
2025-07-16 00:07:18,016 - INFO - تم إعداد واجهة اختيار النظام المحسنة
2025-07-16 00:07:18,016 - INFO - تم <PERSON>عداد التطبيق بنجاح
2025-07-16 00:07:20,016 - INFO - 🤖 بدء الاتصال التلقائي ببوت التلجرام...
2025-07-16 00:07:20,321 - INFO - تم إعداد قائمة البوت بنجاح
2025-07-16 00:07:20,322 - INFO - تم بدء تشغيل بوت التلجرام للكروت
2025-07-16 00:07:23,327 - INFO - 🔄 بدء مزامنة القوالب مع بوت التلجرام...
2025-07-16 00:07:23,363 - INFO - 🔄 تم العثور على 7 قالب للمزامنة
2025-07-16 00:07:23,774 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 00:07:23,775 - INFO - 🔄 تم إرسال قائمة اختيار النظام
2025-07-16 00:08:22,528 - INFO - معالجة callback: select_system_um
2025-07-16 00:08:24,538 - INFO - تم إرسال 2 قالب مفلتر لنظام User Manager
2025-07-16 00:08:26,097 - INFO - معالجة callback: independent_template_um_10
2025-07-16 00:08:27,468 - INFO - 🔄 تبديل النظام تلقائياً من None إلى user_manager
2025-07-16 00:08:27,469 - INFO - 🔄 طلب تبديل النظام من التلجرام: None → user_manager
2025-07-16 00:08:28,148 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-16 00:08:28,240 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:08:28,341 - WARNING - تحذير: فشل في حفظ الإعدادات: 'MikroTikCardGenerator' object has no attribute 'version_combo'
2025-07-16 00:08:28,341 - INFO - تم تعيين النظام الجديد: user_manager
2025-07-16 00:08:28,341 - INFO - 🔄 إعادة بناء الواجهة للنظام: user_manager
2025-07-16 00:08:28,343 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-16 00:08:28,344 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:08:28,914 - INFO - تم إعداد تبويب عرض الكروت لـ User Manager بنجاح
2025-07-16 00:08:29,134 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-16 00:08:29,135 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:08:29,856 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام user_manager
2025-07-16 00:08:29,859 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-16 00:08:29,926 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager
2025-07-16 00:08:29,955 - INFO - تم تسجيل العملية: تبديل النظام من التلجرام - تم تبديل النظام من None إلى user_manager
2025-07-16 00:08:29,965 - INFO - 🔄 بدء مزامنة القالب '10' مع البرنامج الرئيسي
2025-07-16 00:08:29,966 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:08:29,966 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:08:30,328 - INFO - 🖼️ محاولة تحميل صورة الخلفية: img\100100_1.jpg
2025-07-16 00:08:30,331 - INFO - ✅ تم تحميل صورة الخلفية بنجاح: img\100100_1.jpg
2025-07-16 00:08:30,332 - INFO - 🔄 تم جدولة تحديث المعاينة لإظهار الصورة الجديدة
2025-07-16 00:08:30,332 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:08:30,332 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:08:30,427 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 69 إعدادات
2025-07-16 00:08:30,427 - INFO - ✅ تم تطبيق 69 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:08:30,432 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:08:30,450 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:08:30,453 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:08:30,454 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 👤 User Manager - القالب: 10
2025-07-16 00:08:30,760 - INFO - ✅ تمت المزامنة الشاملة للقالب '10' بنجاح
2025-07-16 00:08:30,928 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:08:30,929 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:08:31,054 - INFO - 🔄 إجبار تحديث صورة الخلفية: img\100100_1.jpg
2025-07-16 00:08:31,057 - INFO - ✅ تم إعادة تحميل صورة الخلفية بنجاح من: img\100100_1.jpg
2025-07-16 00:08:43,675 - INFO - معالجة الأمر: /start
2025-07-16 00:08:44,002 - INFO - تم إرسال قائمة اختيار النظام - UM: 2, HS: 5
2025-07-16 00:08:53,293 - INFO - معالجة callback: single_card
2025-07-16 00:08:53,563 - INFO - 🎴 بدء معالجة طلب إنشاء كروت
2025-07-16 00:08:53,564 - INFO - 🔢 عرض خيارات عدد الكروت
2025-07-16 00:08:53,828 - INFO - ✅ تم عرض خيارات عدد الكروت
2025-07-16 00:09:14,464 - INFO - معالجة callback: card_count_1
2025-07-16 00:09:14,684 - INFO - 📋 عرض قوالب Hotspot لإنشاء 1 كرت
2025-07-16 00:09:14,926 - INFO - ✅ تم إرسال قائمة قوالب لإنشاء 1 كرت - 5 قالب
2025-07-16 00:09:18,947 - INFO - معالجة callback: cards_template_1_10
2025-07-16 00:09:19,184 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-16 00:09:19,442 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-16 00:09:19,444 - INFO - 🔄 تبديل النظام - قطع أي اتصالات موجودة...
2025-07-16 00:09:19,445 - INFO - ✅ تبديل النظام - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:09:19,549 - INFO - تم حفظ الإعدادات قبل التبديل
2025-07-16 00:09:19,550 - INFO - تم تعيين النظام الجديد: hotspot
2025-07-16 00:09:19,550 - INFO - ✅ تم تبديل النظام من user_manager إلى hotspot
2025-07-16 00:09:19,550 - INFO - 🔄 إعادة بناء الواجهة للنظام: hotspot
2025-07-16 00:09:19,551 - INFO - 🔄 إعادة بناء الواجهة - قطع أي اتصالات موجودة...
2025-07-16 00:09:19,551 - INFO - ✅ إعادة بناء الواجهة - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:09:19,858 - INFO - تم إعداد تبويب عرض الكروت لـ Hotspot بنجاح
2025-07-16 00:09:19,858 - INFO - 🔄 تحميل الإعدادات - قطع أي اتصالات موجودة...
2025-07-16 00:09:19,859 - INFO - ✅ تحميل الإعدادات - تم تنظيف حالة الاتصال بنجاح
2025-07-16 00:09:19,961 - WARNING - تحذير في تحميل الإعدادات: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:09:19,966 - INFO - تم تسجيل العملية: إعادة بناء الواجهة - تم إعادة بناء الواجهة للنظام hotspot
2025-07-16 00:09:19,968 - INFO - ✅ تم إعادة بناء الواجهة بنجاح
2025-07-16 00:09:19,969 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-16 00:09:20,140 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-16 00:09:20,141 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:09:20,147 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:09:20,245 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:09:20,246 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:09:20,544 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-16 00:09:20,544 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:09:20,547 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:09:20,551 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:09:20,551 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-16 00:09:20,552 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-16 00:09:20,555 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-16 00:09:20,567 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-16 00:09:20,570 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:09:20,571 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-16 00:09:20,572 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-16 00:09:20,572 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-16 00:09:20,572 - INFO - 🏭 بدء توليد الكرت...
2025-07-16 00:09:20,622 - INFO - تم توليد 1 حساب
2025-07-16 00:09:20,623 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 6 (من generate_all)
2025-07-16 00:09:20,629 - ERROR - خطأ في توليد الكرت: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:09:20,629 - INFO - محاولة إعادة التوليد...
2025-07-16 00:09:20,635 - INFO - تم توليد 1 حساب
2025-07-16 00:09:20,635 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 7 (من generate_all)
2025-07-16 00:09:20,640 - ERROR - ❌ خطأ في تنفيذ إنشاء كرت واحد: فشل في توليد الكرت: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:10:09,133 - INFO - معالجة callback: cards_template_1_10
2025-07-16 00:10:09,377 - INFO - 🎴 بدء إنشاء 1 كرت باستخدام القالب: 10
2025-07-16 00:10:09,684 - INFO - 🔄 تفعيل نظام Hotspot مع تحديث الواجهة...
2025-07-16 00:10:09,686 - INFO - النظام hotspot مفعل بالفعل
2025-07-16 00:10:09,686 - INFO - ✅ تم تبديل النظام من hotspot إلى hotspot
2025-07-16 00:10:09,688 - INFO - تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot
2025-07-16 00:10:09,728 - INFO - 📋 تطبيق القالب مع مزامنة شاملة: 10
2025-07-16 00:10:09,729 - INFO - 🔄 بدء المزامنة الشاملة للقالب '10'...
2025-07-16 00:10:09,729 - INFO - 🔄 بدء تطبيق المزامنة الشاملة...
2025-07-16 00:10:09,752 - INFO - 🔄 بدء مزامنة إعدادات الاتصال...
2025-07-16 00:10:09,753 - INFO - ✅ تمت مزامنة إعدادات الاتصال - تم تطبيق 0 إعدادات
2025-07-16 00:10:09,782 - INFO - ✅ تمت المزامنة الشاملة بنجاح - تم تطبيق 72 إعدادات
2025-07-16 00:10:09,782 - INFO - ✅ تم تطبيق 72 إعدادات من القالب '10' بمزامنة شاملة
2025-07-16 00:10:09,785 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:10:09,789 - INFO - ✅ تم تحديث معاينة الكارت مع تحديث إضافي للصورة
2025-07-16 00:10:09,789 - INFO - ✅ تم تطبيق القالب '10' بنجاح
2025-07-16 00:10:09,790 - INFO - ✅ تم تحديث عنوان النافذة: مولد كروت MikroTik - 🌐 Hotspot - القالب: 10
2025-07-16 00:10:09,793 - INFO - ✅ تم تحديث معاينة الكارت
2025-07-16 00:10:09,806 - INFO - ✅ تم تحديث حالة عناصر الواجهة
2025-07-16 00:10:09,813 - INFO - ✅ تم تحديد القالب '10' في قائمة القوالب
2025-07-16 00:10:09,814 - INFO - 🔢 تعيين عدد الكروت إلى 1...
2025-07-16 00:10:09,815 - INFO - ✅ تم تعيين عدد الكروت إلى 1
2025-07-16 00:10:09,815 - INFO - ✅ تم تحديث جميع المتغيرات الداخلية
2025-07-16 00:10:09,815 - INFO - 🏭 بدء توليد الكرت...
2025-07-16 00:10:09,822 - INFO - تم توليد 1 حساب
2025-07-16 00:10:09,824 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 8 (من generate_all)
2025-07-16 00:10:09,830 - ERROR - خطأ في توليد الكرت: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:10:09,830 - INFO - محاولة إعادة التوليد...
2025-07-16 00:10:09,841 - INFO - تم توليد 1 حساب
2025-07-16 00:10:09,843 - INFO - 🔢 تم تحديث آخر رقم تسلسلي إلى: 9 (من generate_all)
2025-07-16 00:10:09,849 - ERROR - ❌ خطأ في تنفيذ إنشاء كرت واحد: فشل في توليد الكرت: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
2025-07-16 00:10:14,860 - INFO - بدء إغلاق التطبيق
2025-07-16 00:10:14,861 - ERROR - خطأ أثناء إغلاق التطبيق: invalid command name ".!canvas.!frame.!notebook.!frame.!labelframe2.!labelframe.!entry"
